#!/usr/bin/env python3
"""
优化结果分析脚本
比较不同优化配置的性能表现
"""

import os
import re
import json
import pandas as pd
from pathlib import Path
from datetime import datetime

class OptimizationAnalyzer:
    def __init__(self, base_dir="TEST1"):
        self.base_dir = Path(base_dir)
        self.results = {}
        
    def extract_roc_from_logs(self, model_dir):
        """从训练日志中提取ROC-AUC值"""
        log_files = list(Path(model_dir).glob("**/trainer_logs/**/*.log"))
        
        for log_file in log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找test/roc行
                roc_matches = re.findall(r'test/roc:\s*([\d.]+)', content)
                if roc_matches:
                    return float(roc_matches[-1])  # 取最后一个值
                    
            except Exception as e:
                continue
                
        return None
    
    def parse_config_file(self, config_path):
        """解析配置文件"""
        config = {}
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if '=' in line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # 尝试转换数值
                        try:
                            if '.' in value:
                                config[key] = float(value)
                            else:
                                config[key] = int(value)
                        except:
                            config[key] = value
                            
        except Exception as e:
            print(f"解析配置文件失败: {e}")
            
        return config
    
    def analyze_model(self, model_name, model_dir):
        """分析单个模型的结果"""
        model_path = self.base_dir / model_dir
        
        if not model_path.exists():
            return None
            
        result = {
            "model_name": model_name,
            "model_dir": str(model_dir),
            "exists": True
        }
        
        # 解析配置文件
        config_file = model_path / "config.toml"
        if config_file.exists():
            result["config"] = self.parse_config_file(config_file)
        else:
            result["config"] = {}
            
        # 提取ROC-AUC
        roc_auc = self.extract_roc_from_logs(model_path)
        result["roc_auc"] = roc_auc
        
        # 检查模型文件
        model_file = model_path / "model_0" / "best.pt"
        result["model_trained"] = model_file.exists()
        
        # 检查预测文件
        pred_file = model_path / "model_0" / "test_predictions.csv"
        result["predictions_available"] = pred_file.exists()
        
        return result
    
    def analyze_all_models(self):
        """分析所有模型"""
        print("🔍 分析所有优化模型...")
        print("=" * 60)
        
        # 定义要分析的模型
        models_to_analyze = [
            ("基线模型", "fragrance_model"),
            ("优化v1-更深更宽", "optimized_v1_deeper_wider"),
            ("优化v2-更长训练", "optimized_v2_longer_training"), 
            ("优化v3-平衡配置", "optimized_v3_balanced"),
            ("集成-种子42", "ensemble_seed_42"),
            ("集成-种子123", "ensemble_seed_123"),
            ("集成-种子456", "ensemble_seed_456"),
            ("集成-种子789", "ensemble_seed_789"),
            ("集成-种子999", "ensemble_seed_999")
        ]
        
        results = []
        
        for model_name, model_dir in models_to_analyze:
            result = self.analyze_model(model_name, model_dir)
            if result:
                results.append(result)
                
                # 显示分析结果
                status = "✅" if result["model_trained"] else "❌"
                roc_str = f"{result['roc_auc']:.4f}" if result['roc_auc'] else "N/A"
                
                print(f"{status} {model_name}")
                print(f"   ROC-AUC: {roc_str}")
                print(f"   目录: {result['model_dir']}")
                print()
        
        self.results = results
        return results
    
    def create_comparison_table(self):
        """创建性能对比表"""
        if not self.results:
            return None
            
        # 过滤有效结果
        valid_results = [r for r in self.results if r["roc_auc"] is not None]
        
        if not valid_results:
            print("❌ 没有找到有效的训练结果")
            return None
            
        # 按性能排序
        valid_results.sort(key=lambda x: x["roc_auc"], reverse=True)
        
        print("\n📊 性能对比表")
        print("=" * 80)
        print(f"{'排名':<4} {'模型名称':<20} {'ROC-AUC':<10} {'提升':<8} {'主要配置':<30}")
        print("-" * 80)
        
        baseline_auc = 0.7225  # 基线性能
        
        for i, result in enumerate(valid_results, 1):
            roc_auc = result["roc_auc"]
            improvement = roc_auc - baseline_auc
            improvement_str = f"+{improvement:.4f}" if improvement > 0 else f"{improvement:.4f}"
            
            # 提取主要配置信息
            config = result.get("config", {})
            config_info = f"epochs={config.get('epochs', 'N/A')}, depth={config.get('depth', 'N/A')}, hidden={config.get('message-hidden-dim', 'N/A')}"
            
            print(f"{i:<4} {result['model_name']:<20} {roc_auc:<10.4f} {improvement_str:<8} {config_info:<30}")
        
        return valid_results
    
    def generate_optimization_report(self):
        """生成优化报告"""
        valid_results = [r for r in self.results if r["roc_auc"] is not None]
        
        if not valid_results:
            return
            
        # 找到最佳结果
        best_result = max(valid_results, key=lambda x: x["roc_auc"])
        baseline_auc = 0.7225
        
        report = {
            "analysis_time": datetime.now().isoformat(),
            "baseline_performance": baseline_auc,
            "best_performance": best_result["roc_auc"],
            "absolute_improvement": best_result["roc_auc"] - baseline_auc,
            "relative_improvement": (best_result["roc_auc"] - baseline_auc) / baseline_auc * 100,
            "best_model": best_result["model_name"],
            "best_config": best_result.get("config", {}),
            "all_results": valid_results
        }
        
        # 保存报告
        report_file = self.base_dir / "optimization_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 优化报告")
        print("=" * 60)
        print(f"基线性能:     {baseline_auc:.4f}")
        print(f"最佳性能:     {best_result['roc_auc']:.4f}")
        print(f"绝对提升:     +{report['absolute_improvement']:.4f}")
        print(f"相对提升:     +{report['relative_improvement']:.1f}%")
        print(f"最佳模型:     {best_result['model_name']}")
        print(f"报告保存至:   {report_file}")
        
        return report
    
    def recommend_next_steps(self):
        """推荐下一步优化方向"""
        valid_results = [r for r in self.results if r["roc_auc"] is not None]
        
        if not valid_results:
            return
            
        best_result = max(valid_results, key=lambda x: x["roc_auc"])
        baseline_auc = 0.7225
        improvement = best_result["roc_auc"] - baseline_auc
        
        print(f"\n💡 优化建议")
        print("=" * 60)
        
        if improvement < 0.02:
            print("🔄 当前提升较小，建议:")
            print("1. 尝试更激进的超参数配置")
            print("2. 增加训练轮数到100+")
            print("3. 尝试集成学习方法")
            print("4. 考虑数据增强技术")
            
        elif improvement < 0.05:
            print("📈 有一定提升，建议:")
            print("1. 基于最佳配置进行微调")
            print("2. 尝试不同的学习率调度")
            print("3. 实验集成学习")
            print("4. 考虑特征工程")
            
        else:
            print("🎉 显著提升！建议:")
            print("1. 基于最佳模型进行集成")
            print("2. 尝试更复杂的架构")
            print("3. 考虑迁移学习")
            print("4. 准备部署应用")

def main():
    """主函数"""
    print("📊 香气分子模型优化结果分析")
    print("=" * 60)
    
    analyzer = OptimizationAnalyzer()
    
    # 分析所有模型
    results = analyzer.analyze_all_models()
    
    if not results:
        print("❌ 没有找到任何模型结果")
        return
    
    # 创建对比表
    analyzer.create_comparison_table()
    
    # 生成报告
    analyzer.generate_optimization_report()
    
    # 推荐下一步
    analyzer.recommend_next_steps()
    
    print(f"\n✅ 分析完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
