#!/usr/bin/env python3
"""
网络架构探索
探索不同的D-MPNN架构配置以优化性能
"""

import pandas as pd
import numpy as np
import subprocess
import os
import json
import time
from datetime import datetime
from sklearn.metrics import roc_auc_score

class ArchitectureExplorer:
    """网络架构探索器"""
    
    def __init__(self):
        self.baseline_auc = 0.7539
        self.results = []
        
    def create_combined_dataset(self):
        """创建合并数据集"""
        if os.path.exists('arch_data.csv'):
            return
            
        train_df = pd.read_csv('train.csv')
        val_df = pd.read_csv('val.csv')
        test_df = pd.read_csv('test.csv')
        
        train_df['split'] = 'train'
        val_df['split'] = 'val'
        test_df['split'] = 'test'
        
        combined_df = pd.concat([train_df, val_df, test_df], ignore_index=True)
        combined_df.to_csv('arch_data.csv', index=False)
        print("✅ 架构探索数据集创建完成")
    
    def define_architecture_variants(self):
        """定义不同的架构变体"""
        
        architectures = []
        
        # 1. 深度探索系列
        for depth in [3, 4, 5, 6, 7, 8]:
            architectures.append({
                'name': f'depth_{depth}',
                'category': 'depth_exploration',
                'depth': depth,
                'hidden_dim': 400,  # 固定其他参数
                'batch_size': 24,
                'dropout': 0.2,
                'epochs': 60,  # 减少训练时间
                'description': f'探索深度={depth}的效果'
            })
        
        # 2. 宽度探索系列
        for hidden_dim in [200, 300, 400, 500, 600, 800]:
            architectures.append({
                'name': f'width_{hidden_dim}',
                'category': 'width_exploration',
                'depth': 5,  # 固定深度
                'hidden_dim': hidden_dim,
                'batch_size': 24,
                'dropout': 0.2,
                'epochs': 60,
                'description': f'探索宽度={hidden_dim}的效果'
            })
        
        # 3. 深度-宽度平衡探索
        balanced_configs = [
            (3, 600), (4, 500), (5, 400), (6, 350), (7, 300), (8, 250)
        ]
        
        for depth, hidden_dim in balanced_configs:
            architectures.append({
                'name': f'balanced_{depth}x{hidden_dim}',
                'category': 'balanced_exploration',
                'depth': depth,
                'hidden_dim': hidden_dim,
                'batch_size': 24,
                'dropout': 0.2,
                'epochs': 60,
                'description': f'平衡配置: 深度={depth}, 宽度={hidden_dim}'
            })
        
        # 4. 正则化强度探索
        for dropout in [0.0, 0.1, 0.15, 0.2, 0.25, 0.3, 0.4]:
            architectures.append({
                'name': f'dropout_{dropout}',
                'category': 'regularization_exploration',
                'depth': 5,
                'hidden_dim': 400,
                'batch_size': 24,
                'dropout': dropout,
                'epochs': 60,
                'description': f'探索dropout={dropout}的效果'
            })
        
        # 5. 批次大小探索
        for batch_size in [8, 16, 20, 24, 28, 32, 40, 48]:
            architectures.append({
                'name': f'batch_{batch_size}',
                'category': 'batch_exploration',
                'depth': 5,
                'hidden_dim': 400,
                'batch_size': batch_size,
                'dropout': 0.2,
                'epochs': 60,
                'description': f'探索批次大小={batch_size}的效果'
            })
        
        return architectures
    
    def train_architecture(self, arch_config):
        """训练特定架构"""
        
        output_dir = f"arch_exploration/{arch_config['name']}"
        os.makedirs("arch_exploration", exist_ok=True)
        
        cmd = [
            'chemprop', 'train',
            '--data-path', 'arch_data.csv',
            '--splits-column', 'split',
            '--task-type', 'classification',
            '--output-dir', output_dir,
            '--epochs', str(arch_config['epochs']),
            '--batch-size', str(arch_config['batch_size']),
            '--message-hidden-dim', str(arch_config['hidden_dim']),
            '--depth', str(arch_config['depth']),
            '--dropout', str(arch_config['dropout']),
            '--init-lr', '0.00001',
            '--max-lr', '0.0001',
            '--final-lr', '0.000005',
            '--warmup-epochs', '5',
            '--data-seed', '42',
            '--save-smiles-splits'
        ]
        
        try:
            print(f"🚀 训练架构: {arch_config['name']} ({arch_config['description']})")
            start_time = time.time()
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=2400)  # 40分钟超时
            
            training_time = time.time() - start_time
            
            if result.returncode == 0:
                # 评估模型
                auc_score = self.evaluate_architecture(output_dir)
                
                if auc_score is not None:
                    improvement = (auc_score - self.baseline_auc) / self.baseline_auc * 100
                    
                    result_info = {
                        'name': arch_config['name'],
                        'category': arch_config['category'],
                        'config': arch_config,
                        'auc': auc_score,
                        'improvement': improvement,
                        'training_time': training_time,
                        'success': True
                    }
                    
                    print(f"✅ {arch_config['name']}: AUC={auc_score:.4f} ({improvement:+.2f}%) 用时={training_time:.1f}s")
                    
                    return result_info
                else:
                    print(f"❌ {arch_config['name']}: 评估失败")
                    return {'name': arch_config['name'], 'success': False}
            else:
                print(f"❌ {arch_config['name']}: 训练失败")
                return {'name': arch_config['name'], 'success': False}
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {arch_config['name']}: 训练超时")
            return {'name': arch_config['name'], 'success': False}
        except Exception as e:
            print(f"❌ {arch_config['name']}: 异常 {e}")
            return {'name': arch_config['name'], 'success': False}
    
    def evaluate_architecture(self, model_dir):
        """评估架构性能"""
        
        pred_path = f"{model_dir}_predictions.csv"
        
        cmd = [
            'chemprop', 'predict',
            '--test-path', 'arch_data.csv',
            '--model-paths', model_dir,
            '--preds-path', pred_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and os.path.exists(pred_path):
                # 计算测试集AUC
                test_df = pd.read_csv('arch_data.csv')
                pred_df = pd.read_csv(pred_path)
                
                test_mask = test_df['split'] == 'test'
                test_data = test_df[test_mask]
                test_preds = pred_df[test_mask]
                
                label_cols = [col for col in test_data.columns if col not in ['smiles', 'split']]
                auc_scores = []
                
                for col in label_cols:
                    if col in test_preds.columns:
                        try:
                            y_true = test_data[col].values
                            y_pred = test_preds[col].values
                            
                            if len(np.unique(y_true)) > 1:
                                auc = roc_auc_score(y_true, y_pred)
                                auc_scores.append(auc)
                        except:
                            continue
                
                if auc_scores:
                    return np.mean(auc_scores)
                    
            return None
            
        except Exception as e:
            return None
    
    def explore_architectures(self, categories=None, max_per_category=None):
        """探索不同架构"""
        
        print("🧠 开始网络架构探索")
        print(f"🎯 目标: 超越基线 {self.baseline_auc:.4f}")
        print("=" * 60)
        
        # 准备数据
        self.create_combined_dataset()
        
        # 获取架构配置
        all_architectures = self.define_architecture_variants()
        
        # 筛选要探索的架构
        if categories:
            architectures = [arch for arch in all_architectures if arch['category'] in categories]
        else:
            architectures = all_architectures
        
        # 限制每个类别的数量
        if max_per_category:
            category_counts = {}
            filtered_architectures = []
            
            for arch in architectures:
                category = arch['category']
                if category_counts.get(category, 0) < max_per_category:
                    filtered_architectures.append(arch)
                    category_counts[category] = category_counts.get(category, 0) + 1
            
            architectures = filtered_architectures
        
        print(f"📋 将探索 {len(architectures)} 种架构配置")
        
        # 训练和评估所有架构
        for i, arch_config in enumerate(architectures, 1):
            print(f"\n进度: {i}/{len(architectures)}")
            result = self.train_architecture(arch_config)
            
            if result['success']:
                self.results.append(result)
        
        # 分析结果
        self.analyze_architecture_results()
        
        return self.results
    
    def analyze_architecture_results(self):
        """分析架构探索结果"""
        
        print(f"\n📊 架构探索结果分析")
        print("=" * 60)
        
        if not self.results:
            print("❌ 没有成功的架构结果")
            return
        
        # 按类别分组分析
        categories = {}
        for result in self.results:
            category = result['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(result)
        
        best_overall = max(self.results, key=lambda x: x['auc'])
        
        print(f"🏆 最佳架构:")
        print(f"   名称: {best_overall['name']}")
        print(f"   AUC: {best_overall['auc']:.4f}")
        print(f"   改进: {best_overall['improvement']:+.2f}%")
        print(f"   配置: 深度={best_overall['config']['depth']}, 宽度={best_overall['config']['hidden_dim']}")
        
        print(f"\n📈 各类别最佳结果:")
        category_best = {}
        
        for category, results in categories.items():
            if results:
                best_in_category = max(results, key=lambda x: x['auc'])
                category_best[category] = best_in_category
                
                print(f"   {category}: {best_in_category['auc']:.4f} ({best_in_category['name']})")
        
        # 参数影响分析
        self.analyze_parameter_effects()
        
        # 保存结果
        results_summary = {
            'exploration_type': 'architecture_exploration',
            'baseline_auc': self.baseline_auc,
            'best_architecture': best_overall,
            'category_best': category_best,
            'all_results': self.results,
            'total_architectures_tested': len(self.results),
            'timestamp': datetime.now().isoformat()
        }
        
        with open('architecture_exploration_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 架构探索结果已保存: architecture_exploration_results.json")
    
    def analyze_parameter_effects(self):
        """分析参数效应"""
        
        print(f"\n🔍 参数效应分析:")
        
        # 深度效应
        depth_results = [r for r in self.results if r['category'] == 'depth_exploration']
        if depth_results:
            depths = [r['config']['depth'] for r in depth_results]
            aucs = [r['auc'] for r in depth_results]
            best_depth_idx = np.argmax(aucs)
            print(f"   最佳深度: {depths[best_depth_idx]} (AUC: {aucs[best_depth_idx]:.4f})")
        
        # 宽度效应
        width_results = [r for r in self.results if r['category'] == 'width_exploration']
        if width_results:
            widths = [r['config']['hidden_dim'] for r in width_results]
            aucs = [r['auc'] for r in width_results]
            best_width_idx = np.argmax(aucs)
            print(f"   最佳宽度: {widths[best_width_idx]} (AUC: {aucs[best_width_idx]:.4f})")
        
        # Dropout效应
        dropout_results = [r for r in self.results if r['category'] == 'regularization_exploration']
        if dropout_results:
            dropouts = [r['config']['dropout'] for r in dropout_results]
            aucs = [r['auc'] for r in dropout_results]
            best_dropout_idx = np.argmax(aucs)
            print(f"   最佳Dropout: {dropouts[best_dropout_idx]} (AUC: {aucs[best_dropout_idx]:.4f})")
        
        # 批次大小效应
        batch_results = [r for r in self.results if r['category'] == 'batch_exploration']
        if batch_results:
            batches = [r['config']['batch_size'] for r in batch_results]
            aucs = [r['auc'] for r in batch_results]
            best_batch_idx = np.argmax(aucs)
            print(f"   最佳批次大小: {batches[best_batch_idx]} (AUC: {aucs[best_batch_idx]:.4f})")

def main():
    """主函数"""
    
    explorer = ArchitectureExplorer()
    
    # 快速探索模式：每个类别只测试几个配置
    results = explorer.explore_architectures(
        categories=['depth_exploration', 'width_exploration', 'regularization_exploration'],
        max_per_category=4  # 每个类别最多4个配置
    )
    
    print(f"\n🎯 架构探索完成!")
    print(f"测试了 {len(results)} 种架构配置")
    
    return results

if __name__ == "__main__":
    main()
