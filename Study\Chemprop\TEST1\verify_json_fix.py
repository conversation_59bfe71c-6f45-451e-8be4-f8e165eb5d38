#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证JSON文件修复
"""

import json
import os

def verify_json_file(filepath):
    """验证JSON文件是否有效"""
    
    print(f"验证文件: {filepath}")
    
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ JSON文件有效")
        print(f"📊 主要内容:")
        
        if 'optimization_summary' in data:
            summary = data['optimization_summary']
            print(f"   方法: {summary.get('method', 'N/A')}")
            print(f"   基线AUC: {summary.get('baseline_auc', 'N/A')}")
            print(f"   优化AUC: {summary.get('optimized_auc', 'N/A')}")
            print(f"   性能提升: {summary.get('improvement_percent', 'N/A')}%")
        
        if 'configuration' in data:
            config = data['configuration']
            print(f"   配置: hidden_dim={config.get('hidden_dim', 'N/A')}, depth={config.get('depth', 'N/A')}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    """主函数"""
    
    print("🔍 验证JSON文件修复")
    print("=" * 50)
    
    # 验证主要的JSON文件
    json_files = [
        'architecture_optimization_report.json',
        'simplified_optimization_report.json',
        'four_optimizations_final_report.json'
    ]
    
    success_count = 0
    
    for json_file in json_files:
        if verify_json_file(json_file):
            success_count += 1
        print("-" * 30)
    
    print(f"\n📋 总结:")
    print(f"   成功验证: {success_count}/{len(json_files)} 个文件")
    
    if success_count == len(json_files):
        print("🎉 所有JSON文件都有效!")
    else:
        print("⚠️ 部分JSON文件有问题")

if __name__ == "__main__":
    main()
