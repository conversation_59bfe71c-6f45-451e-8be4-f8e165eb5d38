#!/usr/bin/env python3
"""
综合优化执行脚本
整合所有优化策略，目标：ROC-AUC > 0.80
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
import subprocess

# 添加当前目录到Python路径
sys.path.append('.')

# 导入自定义模块
try:
    from advanced_optimization_strategy import AdvancedChemPropOptimizer
    from molecular_feature_enhancement import FragranceFeatureEngineer
    from ensemble_learning_strategy import ChemPropEnsemble
except ImportError as e:
    print(f"⚠️ 导入模块失败: {e}")
    print("请确保所有优化脚本都在当前目录下")

class ComprehensiveOptimizer:
    """综合优化器"""
    
    def __init__(self, base_dir="TEST1"):
        self.base_dir = base_dir
        self.results = {}
        self.optimization_log = []
        
    def log_step(self, step_name, status, details=None):
        """记录优化步骤"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'step': step_name,
            'status': status,
            'details': details or {}
        }
        self.optimization_log.append(log_entry)
        print(f"📝 {step_name}: {status}")
        
    def step1_data_analysis(self):
        """步骤1: 数据分析和预处理"""
        self.log_step("数据分析", "开始")
        
        try:
            # 读取训练数据
            train_df = pd.read_csv(f"{self.base_dir}/train.csv")
            val_df = pd.read_csv(f"{self.base_dir}/val.csv")
            test_df = pd.read_csv(f"{self.base_dir}/test.csv")
            
            # 数据统计
            stats = {
                'train_size': len(train_df),
                'val_size': len(val_df),
                'test_size': len(test_df),
                'total_features': len([col for col in train_df.columns if col != 'smiles']),
                'positive_ratio': train_df.iloc[:, 1:].mean().mean()
            }
            
            self.results['data_stats'] = stats
            self.log_step("数据分析", "完成", stats)
            
            return True
            
        except Exception as e:
            self.log_step("数据分析", f"失败: {e}")
            return False
    
    def step2_feature_engineering(self):
        """步骤2: 特征工程"""
        self.log_step("特征工程", "开始")
        
        try:
            engineer = FragranceFeatureEngineer()
            
            # 增强所有数据集
            datasets = ['train.csv', 'val.csv', 'test.csv']
            enhanced_stats = {}
            
            for dataset in datasets:
                input_path = f"{self.base_dir}/{dataset}"
                output_path = f"{self.base_dir}/enhanced_{dataset}"
                
                if os.path.exists(input_path):
                    enhanced_df = engineer.create_enhanced_dataset(input_path, output_path)
                    enhanced_stats[dataset] = {
                        'original_features': len(pd.read_csv(input_path).columns),
                        'enhanced_features': len(enhanced_df.columns),
                        'added_features': len(enhanced_df.columns) - len(pd.read_csv(input_path).columns)
                    }
            
            self.results['feature_engineering'] = enhanced_stats
            self.log_step("特征工程", "完成", enhanced_stats)
            
            return True
            
        except Exception as e:
            self.log_step("特征工程", f"失败: {e}")
            return False
    
    def step3_baseline_training(self):
        """步骤3: 基线模型训练"""
        self.log_step("基线模型训练", "开始")
        
        try:
            # 使用增强特征训练基线模型
            cmd = [
                'chemprop', 'train',
                '--data-path', f'{self.base_dir}/enhanced_train.csv',
                '--separate-val-path', f'{self.base_dir}/enhanced_val.csv',
                '--separate-test-path', f'{self.base_dir}/enhanced_test.csv',
                '--task-type', 'classification',
                '--output-dir', f'{self.base_dir}/baseline_enhanced',
                '--epochs', '80',
                '--batch-size', '24',
                '--message-hidden-dim', '400',
                '--depth', '5',
                '--dropout', '0.2',
                '--init-lr', '0.00001',
                '--max-lr', '0.0001',
                '--final-lr', '0.000005',
                '--warmup-epochs', '5',
                '--save-smiles-splits'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
            
            if result.returncode == 0:
                # 读取测试结果
                test_scores_path = f"{self.base_dir}/baseline_enhanced/test_scores.csv"
                if os.path.exists(test_scores_path):
                    scores_df = pd.read_csv(test_scores_path)
                    baseline_auc = scores_df['auc'].mean()
                    
                    self.results['baseline_enhanced'] = {
                        'auc': baseline_auc,
                        'improvement_over_original': (baseline_auc - 0.7539) / 0.7539 * 100
                    }
                    
                    self.log_step("基线模型训练", f"完成 (AUC: {baseline_auc:.4f})")
                    return True
            
            self.log_step("基线模型训练", "失败")
            return False
            
        except Exception as e:
            self.log_step("基线模型训练", f"失败: {e}")
            return False
    
    def step4_hyperparameter_optimization(self):
        """步骤4: 超参数优化"""
        self.log_step("超参数优化", "开始")
        
        try:
            optimizer = AdvancedChemPropOptimizer(
                f"{self.base_dir}/enhanced_train.csv",
                f"{self.base_dir}/hyperopt_models"
            )
            
            # 执行优化（减少试验次数以节省时间）
            best_score, best_params = optimizer.optimize_with_optuna(n_trials=15)
            
            self.results['hyperparameter_optimization'] = {
                'best_auc': best_score,
                'best_params': best_params,
                'improvement_over_baseline': (best_score - self.results.get('baseline_enhanced', {}).get('auc', 0.7539)) / 0.7539 * 100
            }
            
            self.log_step("超参数优化", f"完成 (最佳AUC: {best_score:.4f})")
            return True
            
        except Exception as e:
            self.log_step("超参数优化", f"失败: {e}")
            return False
    
    def step5_ensemble_learning(self):
        """步骤5: 集成学习"""
        self.log_step("集成学习", "开始")
        
        try:
            # 使用增强特征进行集成学习
            ensemble = ChemPropEnsemble(self.base_dir)
            
            # 修改集成配置以使用增强特征
            original_train_path = f"{self.base_dir}/train.csv"
            enhanced_train_path = f"{self.base_dir}/enhanced_train.csv"
            
            # 临时替换训练数据路径
            if os.path.exists(enhanced_train_path):
                os.rename(original_train_path, f"{original_train_path}.backup")
                os.rename(enhanced_train_path, original_train_path)
            
            try:
                # 训练集成模型
                trained_models = ensemble.train_ensemble(num_seeds=2)
                
                if trained_models:
                    # 评估集成性能
                    results = ensemble.evaluate_ensemble(f"{self.base_dir}/test.csv", f"{self.base_dir}/test.csv")
                    
                    if results:
                        self.results['ensemble_learning'] = {
                            'ensemble_auc': results['mean_auc'],
                            'num_models': len(trained_models),
                            'improvement_over_single': (results['mean_auc'] - self.results.get('hyperparameter_optimization', {}).get('best_auc', 0.7539)) / 0.7539 * 100
                        }
                        
                        self.log_step("集成学习", f"完成 (集成AUC: {results['mean_auc']:.4f})")
                        return True
                
            finally:
                # 恢复原始文件
                if os.path.exists(f"{original_train_path}.backup"):
                    if os.path.exists(original_train_path):
                        os.rename(original_train_path, enhanced_train_path)
                    os.rename(f"{original_train_path}.backup", original_train_path)
            
            self.log_step("集成学习", "失败")
            return False
            
        except Exception as e:
            self.log_step("集成学习", f"失败: {e}")
            return False
    
    def step6_final_evaluation(self):
        """步骤6: 最终评估"""
        self.log_step("最终评估", "开始")
        
        try:
            # 汇总所有结果
            final_results = {
                'original_baseline': 0.7539,
                'optimization_steps': self.results,
                'final_performance': {},
                'recommendations': []
            }
            
            # 找到最佳性能
            best_auc = 0.7539
            best_method = "原始基线"
            
            if 'baseline_enhanced' in self.results:
                enhanced_auc = self.results['baseline_enhanced']['auc']
                if enhanced_auc > best_auc:
                    best_auc = enhanced_auc
                    best_method = "特征增强"
            
            if 'hyperparameter_optimization' in self.results:
                hyperopt_auc = self.results['hyperparameter_optimization']['best_auc']
                if hyperopt_auc > best_auc:
                    best_auc = hyperopt_auc
                    best_method = "超参数优化"
            
            if 'ensemble_learning' in self.results:
                ensemble_auc = self.results['ensemble_learning']['ensemble_auc']
                if ensemble_auc > best_auc:
                    best_auc = ensemble_auc
                    best_method = "集成学习"
            
            final_results['final_performance'] = {
                'best_auc': best_auc,
                'best_method': best_method,
                'total_improvement': (best_auc - 0.7539) / 0.7539 * 100,
                'target_achieved': best_auc >= 0.80
            }
            
            # 生成建议
            recommendations = []
            
            if best_auc >= 0.80:
                recommendations.append("🎉 恭喜！已达到ROC-AUC > 0.80的目标")
            else:
                recommendations.append(f"📈 当前最佳AUC: {best_auc:.4f}，距离目标还需提升 {(0.80 - best_auc) / best_auc * 100:.2f}%")
            
            if best_method == "特征增强":
                recommendations.append("🧬 特征工程效果显著，建议进一步探索分子描述符")
            
            if best_method == "超参数优化":
                recommendations.append("🔧 超参数优化有效，建议增加优化试验次数")
            
            if best_method == "集成学习":
                recommendations.append("🎯 集成学习表现最佳，建议增加模型多样性")
            
            recommendations.extend([
                "📚 考虑使用预训练的分子表示模型",
                "🔄 尝试不同的数据增强技术",
                "⚖️ 探索类别平衡技术",
                "🧠 考虑使用注意力机制"
            ])
            
            final_results['recommendations'] = recommendations
            self.results['final_evaluation'] = final_results
            
            self.log_step("最终评估", "完成")
            return True
            
        except Exception as e:
            self.log_step("最终评估", f"失败: {e}")
            return False
    
    def run_comprehensive_optimization(self):
        """运行综合优化流程"""
        
        print("🚀 开始综合优化流程...")
        print("目标: 将ROC-AUC从0.7539提升至0.80+")
        print("=" * 60)
        
        # 执行优化步骤
        steps = [
            ("数据分析", self.step1_data_analysis),
            ("特征工程", self.step2_feature_engineering),
            ("基线模型训练", self.step3_baseline_training),
            ("超参数优化", self.step4_hyperparameter_optimization),
            ("集成学习", self.step5_ensemble_learning),
            ("最终评估", self.step6_final_evaluation)
        ]
        
        for step_name, step_func in steps:
            print(f"\n🔄 执行步骤: {step_name}")
            success = step_func()
            
            if not success:
                print(f"⚠️ 步骤 {step_name} 失败，继续下一步骤...")
        
        # 保存完整结果
        self.save_results()
        
        # 显示最终报告
        self.display_final_report()
    
    def save_results(self):
        """保存优化结果"""
        
        results_file = f"{self.base_dir}/comprehensive_optimization_results.json"
        
        complete_results = {
            'optimization_log': self.optimization_log,
            'results': self.results,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(complete_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 优化结果已保存: {results_file}")
    
    def display_final_report(self):
        """显示最终报告"""
        
        print("\n" + "=" * 60)
        print("🏆 综合优化最终报告")
        print("=" * 60)
        
        if 'final_evaluation' in self.results:
            final_eval = self.results['final_evaluation']
            
            print(f"📊 原始基线: {final_eval['original_baseline']:.4f}")
            print(f"🎯 最佳性能: {final_eval['final_performance']['best_auc']:.4f}")
            print(f"🚀 最佳方法: {final_eval['final_performance']['best_method']}")
            print(f"📈 总体提升: {final_eval['final_performance']['total_improvement']:.2f}%")
            
            if final_eval['final_performance']['target_achieved']:
                print("🎉 目标达成: ROC-AUC > 0.80 ✅")
            else:
                print("📋 目标未达成，但有显著改进 📈")
            
            print("\n💡 优化建议:")
            for i, rec in enumerate(final_eval['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print("\n" + "=" * 60)

def main():
    """主函数"""
    
    # 检查数据文件
    required_files = ['train.csv', 'val.csv', 'test.csv']
    base_dir = 'TEST1'
    
    for file in required_files:
        if not os.path.exists(f"{base_dir}/{file}"):
            print(f"❌ 缺少必要文件: {base_dir}/{file}")
            return
    
    # 创建优化器并运行
    optimizer = ComprehensiveOptimizer(base_dir)
    optimizer.run_comprehensive_optimization()

if __name__ == "__main__":
    main()
