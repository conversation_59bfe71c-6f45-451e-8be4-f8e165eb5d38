{
  "optimization_summary": {
    "method": "架构优化",
    "key_change": "hidden_dim: 400 → 500",
    "baseline_auc": 0.7539,
    "optimized_auc": 0.8600697302488809,
    "improvement_percent": 14.082733817333981,
    "training_time_seconds": 1246.1505579948425,
    "model_directory": "optimized_architecture_model",
    "optimization_date": "2025-08-03T14:41:45.271611"
  },
  "configuration": {
    "epochs": 80,
    "batch_size": 24,
    "hidden_dim": 500,
    "depth": 5,
    "dropout": 0.2,
    "data_seed": 42,
    "pytorch_seed": 42
  },
  "experimental_basis": {
    "source": "simplified_optimization.py实验结果",
    "expected_improvement": 11.86,
    "actual_improvement": 14.082733817333981,
    "expectation_met": 