#!/usr/bin/env python3
"""
快速特征对比实验
使用较少的训练轮数快速验证特征增强效果
"""

import pandas as pd
import numpy as np
from rdkit import Chem
from rdkit.Chem import Descriptors, Crippen
from rdkit.Chem.rdMolDescriptors import CalcTPSA, CalcNumRotatableBonds
import subprocess
import os
import json
from sklearn.metrics import roc_auc_score
import time

class QuickFeatureComparison:
    """快速特征对比器"""
    
    def __init__(self):
        self.results = {}
        
    def extract_minimal_features(self, smiles_list):
        """提取最小化香气特征"""
        features = []
        
        for smiles in smiles_list:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                features.append([0, 0, 0, 0, 0])
                continue
            
            # 5个核心香气特征
            mw = Descriptors.MolWt(mol)
            volatility = 1000 / (mw + 50)  # 挥发性指数
            logp = Crippen.MolLogP(mol)    # 疏水性
            tpsa = CalcTPSA(mol)
            psa_ratio = tpsa / max(mw, 1) * 100  # 极性表面积比例
            rotatable_bonds = CalcNumRotatableBonds(mol)
            flexibility = rotatable_bonds / max(mol.GetNumBonds(), 1)  # 分子柔性
            aromatic_atoms = sum(1 for atom in mol.GetAtoms() if atom.GetIsAromatic())
            aromaticity = aromatic_atoms / max(mol.GetNumAtoms(), 1)  # 芳香性指数
            
            features.append([volatility, logp, psa_ratio, flexibility, aromaticity])
        
        return pd.DataFrame(features, columns=[
            'volatility_index', 'hydrophobicity', 'polar_surface_ratio', 
            'molecular_flexibility', 'aromaticity_index'
        ])
    
    def prepare_datasets(self):
        """准备对比数据集"""
        print("📊 准备对比数据集...")
        
        # 读取原始数据
        train_df = pd.read_csv('train.csv')
        val_df = pd.read_csv('val.csv')
        test_df = pd.read_csv('test.csv')
        
        # 创建原始合并数据集
        train_df['split'] = 'train'
        val_df['split'] = 'val'
        test_df['split'] = 'test'
        
        original_combined = pd.concat([train_df, val_df, test_df], ignore_index=True)
        original_combined.to_csv('quick_original.csv', index=False)
        
        # 创建增强特征数据集
        print("🧬 提取香气特征...")
        all_smiles = original_combined['smiles'].tolist()
        fragrance_features = self.extract_minimal_features(all_smiles)
        
        enhanced_combined = pd.concat([original_combined, fragrance_features], axis=1)
        enhanced_combined.to_csv('quick_enhanced.csv', index=False)
        
        print(f"✅ 数据集准备完成")
        print(f"   原始特征数: {len(original_combined.columns)}")
        print(f"   增强特征数: {len(enhanced_combined.columns)} (+{len(fragrance_features.columns)})")
        
        return original_combined, enhanced_combined
    
    def quick_train(self, data_path, output_dir, model_name):
        """快速训练模型（减少训练轮数）"""
        print(f"🚀 快速训练 {model_name}...")
        
        cmd = [
            'chemprop', 'train',
            '--data-path', data_path,
            '--splits-column', 'split',
            '--task-type', 'classification',
            '--output-dir', output_dir,
            '--epochs', '20',  # 大幅减少训练轮数
            '--batch-size', '32',
            '--message-hidden-dim', '300',
            '--depth', '3',
            '--dropout', '0.1',
            '--init-lr', '0.0001',
            '--max-lr', '0.001',
            '--final-lr', '0.00001',
            '--warmup-epochs', '2',
            '--data-seed', '42',
            '--save-smiles-splits'
        ]
        
        start_time = time.time()
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
            
            training_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ {model_name} 训练完成 (用时: {training_time:.1f}秒)")
                return True, training_time
            else:
                print(f"❌ {model_name} 训练失败")
                print(f"错误信息: {result.stderr}")
                return False, training_time
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {model_name} 训练超时")
            return False, time.time() - start_time
        except Exception as e:
            print(f"❌ {model_name} 训练异常: {e}")
            return False, time.time() - start_time
    
    def evaluate_model(self, model_dir, test_data_path, model_name):
        """评估模型性能"""
        print(f"📊 评估 {model_name}...")
        
        # 进行预测
        pred_path = f"{model_dir}_predictions.csv"
        
        cmd = [
            'chemprop', 'predict',
            '--test-path', test_data_path,
            '--model-paths', model_dir,
            '--preds-path', pred_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and os.path.exists(pred_path):
                # 读取预测结果
                pred_df = pd.read_csv(pred_path)
                test_df = pd.read_csv(test_data_path)
                
                # 只评估测试集
                test_mask = test_df['split'] == 'test'
                test_data = test_df[test_mask]
                test_preds = pred_df[test_mask]
                
                # 计算AUC
                label_cols = [col for col in test_data.columns if col not in ['smiles', 'split']]
                auc_scores = []
                
                for col in label_cols:
                    if col in test_preds.columns:
                        try:
                            y_true = test_data[col].values
                            y_pred = test_preds[col].values
                            
                            # 只计算有效标签的AUC
                            if len(np.unique(y_true)) > 1:
                                auc = roc_auc_score(y_true, y_pred)
                                auc_scores.append(auc)
                        except:
                            continue
                
                if auc_scores:
                    mean_auc = np.mean(auc_scores)
                    print(f"📈 {model_name} 平均AUC: {mean_auc:.4f}")
                    return mean_auc
                else:
                    print(f"❌ {model_name} AUC计算失败")
                    return None
            else:
                print(f"❌ {model_name} 预测失败")
                return None
                
        except Exception as e:
            print(f"❌ {model_name} 评估异常: {e}")
            return None
    
    def run_comparison(self):
        """运行完整对比实验"""
        print("🔬 开始快速特征对比实验")
        print("=" * 60)
        
        # 1. 准备数据集
        original_data, enhanced_data = self.prepare_datasets()
        
        # 2. 训练原始模型
        print("\n🤖 步骤1: 训练原始模型")
        original_success, original_time = self.quick_train(
            'quick_original.csv', 
            'quick_original_model', 
            '原始模型'
        )
        
        # 3. 训练增强模型
        print("\n🧬 步骤2: 训练增强特征模型")
        enhanced_success, enhanced_time = self.quick_train(
            'quick_enhanced.csv', 
            'quick_enhanced_model', 
            '增强特征模型'
        )
        
        # 4. 评估模型
        print("\n📊 步骤3: 评估模型性能")
        
        original_auc = None
        enhanced_auc = None
        
        if original_success:
            original_auc = self.evaluate_model(
                'quick_original_model', 
                'quick_original.csv', 
                '原始模型'
            )
        
        if enhanced_success:
            enhanced_auc = self.evaluate_model(
                'quick_enhanced_model', 
                'quick_enhanced.csv', 
                '增强特征模型'
            )
        
        # 5. 分析结果
        print("\n📋 步骤4: 结果分析")
        print("=" * 60)
        
        self.results = {
            'experiment_type': 'quick_feature_comparison',
            'training_epochs': 20,
            'original_model': {
                'success': original_success,
                'training_time': original_time,
                'auc': original_auc
            },
            'enhanced_model': {
                'success': enhanced_success,
                'training_time': enhanced_time,
                'auc': enhanced_auc
            },
            'features_added': [
                'volatility_index',
                'hydrophobicity', 
                'polar_surface_ratio',
                'molecular_flexibility',
                'aromaticity_index'
            ]
        }
        
        if original_auc is not None and enhanced_auc is not None:
            improvement = (enhanced_auc - original_auc) / original_auc * 100
            self.results['improvement_percent'] = improvement
            
            print(f"🔍 详细对比结果:")
            print(f"   原始模型 AUC:        {original_auc:.4f}")
            print(f"   增强特征模型 AUC:    {enhanced_auc:.4f}")
            print(f"   性能变化:            {improvement:+.2f}%")
            print(f"   原始模型训练时间:    {original_time:.1f}秒")
            print(f"   增强模型训练时间:    {enhanced_time:.1f}秒")
            
            print(f"\n💡 结论分析:")
            if improvement > 1.0:
                print(f"✅ 特征增强有效！提升了 {improvement:.2f}%")
                print("🧬 这表明D-MPNN确实遗漏了重要的香气相关信息")
                print("📈 建议在完整训练中使用特征增强")
            elif improvement > -1.0:
                print(f"🤔 特征增强效果微小 ({improvement:.2f}%)")
                print("⚖️ 可能需要更多训练轮数才能看到明显差异")
                print("🔬 建议进行更长时间的训练验证")
            else:
                print(f"❌ 特征增强可能有负面影响 ({improvement:.2f}%)")
                print("🤖 D-MPNN可能已经足够强大，不需要额外特征")
                print("⚠️ 或者添加的特征引入了噪声")
                
        else:
            print("❌ 无法完成完整对比，部分模型训练失败")
            
        # 6. 保存结果
        with open('quick_comparison_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 实验结果已保存: quick_comparison_results.json")
        
        return self.results

def main():
    """主函数"""
    
    # 检查必要文件
    required_files = ['train.csv', 'val.csv', 'test.csv']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return
    
    # 运行快速对比实验
    comparator = QuickFeatureComparison()
    results = comparator.run_comparison()
    
    print("\n" + "=" * 60)
    print("🎯 快速实验总结:")
    print("   这个实验用较少的训练轮数快速验证特征增强效果")
    print("   如果显示有效果，建议进行完整的长时间训练")
    print("   如果无明显效果，说明D-MPNN已经足够强大")
    print("=" * 60)

if __name__ == "__main__":
    main()
