#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香气分子数据库分析脚本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_fragrance_data():
    """分析香气分子数据库"""
    
    # 读取数据
    print("正在读取数据...")
    df = pd.read_csv('goodscents_V3.csv')
    
    print('=== 香气分子数据库分析 ===')
    print(f'数据集形状: {df.shape}')
    print(f'分子数量: {df.shape[0]}')
    print(f'特征数量: {df.shape[1]}')
    
    print('\n=== 列信息 ===')
    print('前几列:', df.columns[:5].tolist())
    print('香气描述符数量:', len(df.columns) - 2)  # 减去SMILES和描述列
    
    print('\n=== SMILES列分析 ===')
    print('SMILES示例:')
    for i in range(5):
        print(f'  {df.iloc[i, 0]}')
    
    print('\n=== 香气描述分析 ===')
    print('描述示例:')
    for i in range(5):
        print(f'  {df.iloc[i, 1]}')
    
    print('\n=== 标签分布分析 ===')
    # 分析二进制标签
    label_cols = df.columns[2:]  # 从第3列开始是香气标签
    label_sums = df[label_cols].sum()
    print(f'最常见的香气特征 (前10个):')
    top_10 = label_sums.sort_values(ascending=False).head(10)
    for feature, count in top_10.items():
        print(f'  {feature}: {count} ({count/len(df)*100:.1f}%)')
    
    print(f'\n每个分子的平均标签数: {df[label_cols].sum(axis=1).mean():.2f}')
    print(f'标签数范围: {df[label_cols].sum(axis=1).min()} - {df[label_cols].sum(axis=1).max()}')
    
    print('\n=== 缺失值检查 ===')
    missing = df.isnull().sum()
    if missing.sum() > 0:
        print('存在缺失值的列:')
        print(missing[missing > 0])
    else:
        print('无缺失值')
    
    # 保存分析结果
    analysis_results = {
        'total_molecules': df.shape[0],
        'total_features': df.shape[1],
        'odor_features': len(label_cols),
        'avg_labels_per_molecule': df[label_cols].sum(axis=1).mean(),
        'top_10_features': top_10.to_dict(),
        'has_missing_values': missing.sum() > 0
    }
    
    return df, analysis_results

if __name__ == "__main__":
    df, results = analyze_fragrance_data()
    print("\n分析完成！")
