#!/usr/bin/env python3
"""
香气分子模型优化实验脚本
自动运行多个优化配置并比较性能
"""

import subprocess
import json
import time
import os
from pathlib import Path
from datetime import datetime

class FragranceModelOptimizer:
    def __init__(self, base_dir="TEST1"):
        self.base_dir = Path(base_dir)
        self.results = []
        
    def run_experiment(self, config_name, params):
        """运行单个优化实验"""
        print(f"\n🚀 开始实验: {config_name}")
        print("=" * 60)
        
        # 构建命令
        cmd = [
            "chemprop", "train",
            "--data-path", str(self.base_dir / "train.csv"),
            "--task-type", "classification",
            "--output-dir", str(self.base_dir / f"optimized_{config_name}"),
            "--num-workers", "0"
        ]
        
        # 添加参数
        for key, value in params.items():
            if isinstance(value, list):
                cmd.extend([f"--{key}"] + [str(v) for v in value])
            else:
                cmd.extend([f"--{key}", str(value)])
        
        print(f"命令: {' '.join(cmd)}")
        print()
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 运行训练
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                cwd=os.getcwd(),
                timeout=3600  # 1小时超时
            )
            
            # 记录结束时间
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                # 解析结果
                roc_auc = self.extract_roc_auc(result.stdout)
                
                experiment_result = {
                    "config_name": config_name,
                    "params": params,
                    "roc_auc": roc_auc,
                    "duration_minutes": duration / 60,
                    "status": "success",
                    "timestamp": datetime.now().isoformat()
                }
                
                print(f"✅ 实验成功完成!")
                print(f"📊 ROC-AUC: {roc_auc:.4f}")
                print(f"⏱️ 训练时间: {duration/60:.1f} 分钟")
                
            else:
                experiment_result = {
                    "config_name": config_name,
                    "params": params,
                    "roc_auc": None,
                    "duration_minutes": duration / 60,
                    "status": "failed",
                    "error": result.stderr,
                    "timestamp": datetime.now().isoformat()
                }
                
                print(f"❌ 实验失败!")
                print(f"错误信息: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            experiment_result = {
                "config_name": config_name,
                "params": params,
                "roc_auc": None,
                "duration_minutes": 60,
                "status": "timeout",
                "timestamp": datetime.now().isoformat()
            }
            print(f"⏰ 实验超时!")
            
        except Exception as e:
            experiment_result = {
                "config_name": config_name,
                "params": params,
                "roc_auc": None,
                "duration_minutes": 0,
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            print(f"💥 实验异常: {e}")
        
        self.results.append(experiment_result)
        return experiment_result
    
    def extract_roc_auc(self, output):
        """从ChemProp输出中提取ROC-AUC值"""
        lines = output.split('\n')
        for line in lines:
            if 'test/roc:' in line:
                try:
                    return float(line.split('test/roc:')[1].strip())
                except:
                    pass
        return None
    
    def run_quick_optimization(self):
        """运行快速优化实验"""
        print("\n🎯 阶段1: 快速超参数优化")
        print("=" * 60)
        
        # 实验配置
        experiments = {
            "deeper_wider": {
                "epochs": 50,
                "batch-size": 16,
                "message-hidden-dim": 500,
                "depth": 4,
                "dropout": 0.15,
                "ffn-hidden-dim": 400,
                "ffn-num-layers": 2,
                "init-lr": 0.00005,
                "max-lr": 0.0005,
                "warmup-epochs": 5,
                "split": "RANDOM",
                "split-sizes": [0.8, 0.1, 0.1],
                "data-seed": 42
            },
            
            "longer_training": {
                "epochs": 80,
                "batch-size": 24,
                "message-hidden-dim": 400,
                "depth": 5,
                "dropout": 0.2,
                "init-lr": 0.00003,
                "max-lr": 0.0003,
                "final-lr": 0.00001,
                "warmup-epochs": 8,
                "split": "RANDOM",
                "split-sizes": [0.8, 0.1, 0.1],
                "data-seed": 42
            },
            
            "balanced_approach": {
                "epochs": 60,
                "batch-size": 20,
                "message-hidden-dim": 450,
                "depth": 4,
                "dropout": 0.18,
                "ffn-hidden-dim": 350,
                "ffn-num-layers": 2,
                "init-lr": 0.00004,
                "max-lr": 0.0004,
                "warmup-epochs": 6,
                "split": "RANDOM",
                "split-sizes": [0.8, 0.1, 0.1],
                "data-seed": 42
            }
        }
        
        # 运行实验
        for config_name, params in experiments.items():
            self.run_experiment(config_name, params)
            
    def run_ensemble_experiments(self):
        """运行集成学习实验"""
        print("\n🎯 阶段2: 集成学习优化")
        print("=" * 60)
        
        # 最佳配置的多种子版本
        best_config = {
            "epochs": 60,
            "batch-size": 16,
            "message-hidden-dim": 500,
            "depth": 4,
            "dropout": 0.15,
            "ffn-hidden-dim": 400,
            "ffn-num-layers": 2,
            "init-lr": 0.00005,
            "max-lr": 0.0005,
            "warmup-epochs": 5,
            "split": "RANDOM",
            "split-sizes": [0.8, 0.1, 0.1]
        }
        
        # 不同随机种子
        seeds = [42, 123, 456, 789, 999]
        
        for seed in seeds:
            config = best_config.copy()
            config["data-seed"] = seed
            self.run_experiment(f"ensemble_seed_{seed}", config)
    
    def save_results(self):
        """保存实验结果"""
        results_file = self.base_dir / "optimization_results.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 结果已保存到: {results_file}")
    
    def analyze_results(self):
        """分析实验结果"""
        print("\n📊 实验结果分析")
        print("=" * 60)
        
        # 过滤成功的实验
        successful_results = [r for r in self.results if r["status"] == "success" and r["roc_auc"] is not None]
        
        if not successful_results:
            print("❌ 没有成功的实验结果")
            return
        
        # 按性能排序
        successful_results.sort(key=lambda x: x["roc_auc"], reverse=True)
        
        print(f"✅ 成功完成 {len(successful_results)} 个实验")
        print()
        
        # 显示前5名结果
        print("🏆 性能排行榜 (Top 5):")
        print("-" * 60)
        for i, result in enumerate(successful_results[:5], 1):
            print(f"{i}. {result['config_name']}")
            print(f"   ROC-AUC: {result['roc_auc']:.4f}")
            print(f"   训练时间: {result['duration_minutes']:.1f} 分钟")
            print()
        
        # 基线对比
        baseline_auc = 0.7225
        best_result = successful_results[0]
        improvement = best_result["roc_auc"] - baseline_auc
        
        print(f"📈 性能提升分析:")
        print(f"   基线性能: {baseline_auc:.4f}")
        print(f"   最佳性能: {best_result['roc_auc']:.4f}")
        print(f"   绝对提升: +{improvement:.4f}")
        print(f"   相对提升: +{improvement/baseline_auc*100:.1f}%")
        
        return successful_results

def main():
    """主函数"""
    print("🎉 香气分子模型优化实验")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 创建优化器
    optimizer = FragranceModelOptimizer()
    
    try:
        # 阶段1: 快速优化
        optimizer.run_quick_optimization()
        
        # 分析阶段1结果
        print("\n📊 阶段1结果分析")
        stage1_results = optimizer.analyze_results()
        
        # 如果阶段1有好结果，继续阶段2
        if stage1_results and stage1_results[0]["roc_auc"] > 0.75:
            print("\n🎯 阶段1表现良好，继续集成学习实验...")
            optimizer.run_ensemble_experiments()
        
        # 最终分析
        print("\n🏁 最终结果分析")
        final_results = optimizer.analyze_results()
        
        # 保存结果
        optimizer.save_results()
        
        print(f"\n✅ 优化实验完成!")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 实验被用户中断")
        optimizer.save_results()
    except Exception as e:
        print(f"\n💥 实验异常: {e}")
        optimizer.save_results()

if __name__ == "__main__":
    main()
