batch-size = 24
data-path = simple_combined.csv
output-dir = simple_hyper_lower_lr
message-hidden-dim = 400
depth = 5
dropout = 0.2
splits-column = split
task-type = classification
init-lr = 5e-06
max-lr = 5e-05
final-lr = 2.5e-06
epochs = 40
data-seed = 42
num-workers = 0
accelerator = auto
devices = auto
rxn-mode = REAC_DIFF
multi-hot-atom-featurizer-mode = V2
frzn-ffn-layers = 0
ensemble-size = 1
aggregation = norm
aggregation-norm = 100
activation = RELU
ffn-hidden-dim = 300
ffn-num-layers = 1
multiclass-num-classes = 3
atom-ffn-hidden-dim = 300
atom-ffn-num-layers = 1
atom-multiclass-num-classes = 3
bond-ffn-hidden-dim = 300
bond-ffn-num-layers = 1
bond-multiclass-num-classes = 3
atom-constrainer-ffn-hidden-dim = 300
atom-constrainer-ffn-num-layers = 1
bond-constrainer-ffn-hidden-dim = 300
bond-constrainer-ffn-num-layers = 1
v-kl = 0.0
eps = 1e-08
alpha = 0.1
tracking-metric = val_loss
warmup-epochs = 2
split = RANDOM
split-sizes = [0.8, 0.1, 0.1]
split-key-molecule = 0
num-replicates = 1
