#!/usr/bin/env python3
"""
数据质量提升
分析和改进香气分子数据集的质量
"""

import pandas as pd
import numpy as np
from rdkit import Chem
from rdkit.Chem import Descriptors
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import json
import os

class DataQualityEnhancer:
    """数据质量增强器"""
    
    def __init__(self):
        self.quality_report = {}
        
    def analyze_data_quality(self):
        """全面分析数据质量"""
        
        print("📊 开始数据质量分析...")
        print("=" * 60)
        
        # 读取数据
        train_df = pd.read_csv('train.csv')
        val_df = pd.read_csv('val.csv')
        test_df = pd.read_csv('test.csv')
        
        all_data = pd.concat([train_df, val_df, test_df], ignore_index=True)
        
        # 1. 基础数据统计
        self.basic_statistics(all_data)
        
        # 2. SMILES质量检查
        self.smiles_quality_check(all_data)
        
        # 3. 标签分布分析
        self.label_distribution_analysis(all_data)
        
        # 4. 数据一致性检查
        self.consistency_check(train_df, val_df, test_df)
        
        # 5. 分子多样性分析
        self.molecular_diversity_analysis(all_data)
        
        return self.quality_report
    
    def basic_statistics(self, df):
        """基础数据统计"""
        
        print("📈 基础数据统计:")
        
        stats = {
            'total_molecules': len(df),
            'unique_smiles': df['smiles'].nunique(),
            'duplicate_smiles': len(df) - df['smiles'].nunique(),
            'total_features': len(df.columns) - 1,  # 除去SMILES列
        }
        
        print(f"   总分子数: {stats['total_molecules']}")
        print(f"   唯一SMILES: {stats['unique_smiles']}")
        print(f"   重复SMILES: {stats['duplicate_smiles']}")
        print(f"   特征数量: {stats['total_features']}")
        
        self.quality_report['basic_statistics'] = stats
    
    def smiles_quality_check(self, df):
        """SMILES质量检查"""
        
        print(f"\n🧬 SMILES质量检查:")
        
        invalid_smiles = []
        molecular_properties = []
        
        for idx, smiles in enumerate(df['smiles']):
            mol = Chem.MolFromSmiles(smiles)
            
            if mol is None:
                invalid_smiles.append((idx, smiles))
            else:
                # 计算分子性质
                mw = Descriptors.MolWt(mol)
                logp = Descriptors.MolLogP(mol)
                hbd = Descriptors.NumHDonors(mol)
                hba = Descriptors.NumHAcceptors(mol)
                
                molecular_properties.append({
                    'mw': mw,
                    'logp': logp,
                    'hbd': hbd,
                    'hba': hba
                })
        
        print(f"   无效SMILES: {len(invalid_smiles)}")
        
        if molecular_properties:
            props_df = pd.DataFrame(molecular_properties)
            
            print(f"   分子量范围: {props_df['mw'].min():.1f} - {props_df['mw'].max():.1f}")
            print(f"   LogP范围: {props_df['logp'].min():.2f} - {props_df['logp'].max():.2f}")
            print(f"   氢键供体范围: {props_df['hbd'].min():.0f} - {props_df['hbd'].max():.0f}")
            print(f"   氢键受体范围: {props_df['hba'].min():.0f} - {props_df['hba'].max():.0f}")
        
        self.quality_report['smiles_quality'] = {
            'invalid_count': len(invalid_smiles),
            'invalid_indices': [idx for idx, _ in invalid_smiles],
            'molecular_properties_stats': props_df.describe().to_dict() if molecular_properties else None
        }
    
    def label_distribution_analysis(self, df):
        """标签分布分析"""
        
        print(f"\n🏷️ 标签分布分析:")
        
        label_cols = [col for col in df.columns if col != 'smiles']
        
        label_stats = {}
        imbalanced_labels = []
        
        for col in label_cols:
            positive_ratio = df[col].mean()
            total_count = len(df[col])
            positive_count = df[col].sum()
            negative_count = total_count - positive_count
            
            label_stats[col] = {
                'positive_ratio': positive_ratio,
                'positive_count': int(positive_count),
                'negative_count': int(negative_count),
                'imbalance_ratio': max(positive_count, negative_count) / min(positive_count, negative_count) if min(positive_count, negative_count) > 0 else float('inf')
            }
            
            # 标记严重不平衡的标签 (比例 > 10:1)
            if label_stats[col]['imbalance_ratio'] > 10:
                imbalanced_labels.append(col)
        
        print(f"   总标签数: {len(label_cols)}")
        print(f"   平均正例比例: {np.mean([stats['positive_ratio'] for stats in label_stats.values()]):.3f}")
        print(f"   严重不平衡标签: {len(imbalanced_labels)}")
        
        if imbalanced_labels:
            print(f"   不平衡标签示例: {imbalanced_labels[:5]}")
        
        self.quality_report['label_distribution'] = {
            'total_labels': len(label_cols),
            'label_stats': label_stats,
            'imbalanced_labels': imbalanced_labels,
            'mean_positive_ratio': np.mean([stats['positive_ratio'] for stats in label_stats.values()])
        }
    
    def consistency_check(self, train_df, val_df, test_df):
        """数据一致性检查"""
        
        print(f"\n🔍 数据一致性检查:")
        
        # 检查数据集间的重叠
        train_smiles = set(train_df['smiles'])
        val_smiles = set(val_df['smiles'])
        test_smiles = set(test_df['smiles'])
        
        train_val_overlap = len(train_smiles & val_smiles)
        train_test_overlap = len(train_smiles & test_smiles)
        val_test_overlap = len(val_smiles & test_smiles)
        
        print(f"   训练-验证重叠: {train_val_overlap}")
        print(f"   训练-测试重叠: {train_test_overlap}")
        print(f"   验证-测试重叠: {val_test_overlap}")
        
        # 检查标签一致性
        label_cols = [col for col in train_df.columns if col != 'smiles']
        
        train_label_dist = train_df[label_cols].mean()
        val_label_dist = val_df[label_cols].mean()
        test_label_dist = test_df[label_cols].mean()
        
        # 计算分布差异
        train_val_diff = np.abs(train_label_dist - val_label_dist).mean()
        train_test_diff = np.abs(train_label_dist - test_label_dist).mean()
        
        print(f"   训练-验证标签分布差异: {train_val_diff:.4f}")
        print(f"   训练-测试标签分布差异: {train_test_diff:.4f}")
        
        self.quality_report['consistency'] = {
            'smiles_overlaps': {
                'train_val': train_val_overlap,
                'train_test': train_test_overlap,
                'val_test': val_test_overlap
            },
            'label_distribution_differences': {
                'train_val_diff': train_val_diff,
                'train_test_diff': train_test_diff
            }
        }
    
    def molecular_diversity_analysis(self, df):
        """分子多样性分析"""
        
        print(f"\n🌈 分子多样性分析:")
        
        # 计算分子指纹多样性
        from rdkit.Chem import rdMolDescriptors
        from rdkit import DataStructs
        
        valid_mols = []
        for smiles in df['smiles']:
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                valid_mols.append(mol)
        
        if len(valid_mols) > 100:  # 采样以节省计算时间
            sample_size = min(1000, len(valid_mols))
            sampled_mols = np.random.choice(valid_mols, sample_size, replace=False)
        else:
            sampled_mols = valid_mols
        
        # 计算Tanimoto相似性
        fps = [rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2) for mol in sampled_mols]
        
        similarities = []
        for i in range(min(100, len(fps))):  # 限制计算量
            for j in range(i+1, min(100, len(fps))):
                sim = DataStructs.TanimotoSimilarity(fps[i], fps[j])
                similarities.append(sim)
        
        if similarities:
            mean_similarity = np.mean(similarities)
            print(f"   平均Tanimoto相似性: {mean_similarity:.3f}")
            print(f"   分子多样性评分: {1 - mean_similarity:.3f}")
        
        self.quality_report['molecular_diversity'] = {
            'mean_tanimoto_similarity': np.mean(similarities) if similarities else None,
            'diversity_score': 1 - np.mean(similarities) if similarities else None,
            'sample_size': len(sampled_mols)
        }
    
    def create_cleaned_dataset(self):
        """创建清理后的数据集"""
        
        print(f"\n🧹 创建清理后的数据集:")
        
        # 读取原始数据
        train_df = pd.read_csv('train.csv')
        val_df = pd.read_csv('val.csv')
        test_df = pd.read_csv('test.csv')
        
        datasets = [('train', train_df), ('val', val_df), ('test', test_df)]
        cleaned_datasets = []
        
        for name, df in datasets:
            print(f"   清理 {name} 数据集...")
            
            # 1. 移除无效SMILES
            valid_mask = df['smiles'].apply(lambda x: Chem.MolFromSmiles(x) is not None)
            cleaned_df = df[valid_mask].copy()
            
            # 2. 移除重复SMILES
            cleaned_df = cleaned_df.drop_duplicates(subset=['smiles'])
            
            # 3. 移除极端不平衡的标签 (正例比例 < 0.01 或 > 0.99)
            label_cols = [col for col in cleaned_df.columns if col != 'smiles']
            
            balanced_labels = []
            for col in label_cols:
                positive_ratio = cleaned_df[col].mean()
                if 0.01 <= positive_ratio <= 0.99:
                    balanced_labels.append(col)
            
            # 保留平衡的标签
            cleaned_df = cleaned_df[['smiles'] + balanced_labels]
            
            print(f"     原始: {len(df)} 分子, {len(df.columns)-1} 标签")
            print(f"     清理后: {len(cleaned_df)} 分子, {len(cleaned_df.columns)-1} 标签")
            
            # 保存清理后的数据
            cleaned_df.to_csv(f'cleaned_{name}.csv', index=False)
            cleaned_datasets.append((name, cleaned_df))
        
        print(f"✅ 清理后的数据集已保存")
        
        return cleaned_datasets
    
    def generate_quality_report(self):
        """生成质量报告"""
        
        # 分析数据质量
        self.analyze_data_quality()
        
        # 创建清理后的数据集
        cleaned_datasets = self.create_cleaned_dataset()
        
        # 保存完整报告
        with open('data_quality_report.json', 'w', encoding='utf-8') as f:
            json.dump(self.quality_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 数据质量报告已保存: data_quality_report.json")
        
        # 生成改进建议
        self.generate_improvement_suggestions()
        
        return self.quality_report
    
    def generate_improvement_suggestions(self):
        """生成数据改进建议"""
        
        print(f"\n💡 数据质量改进建议:")
        print("=" * 60)
        
        suggestions = []
        
        # 基于分析结果生成建议
        if self.quality_report.get('smiles_quality', {}).get('invalid_count', 0) > 0:
            suggestions.append("🔧 移除或修复无效的SMILES结构")
        
        if len(self.quality_report.get('label_distribution', {}).get('imbalanced_labels', [])) > 0:
            suggestions.append("⚖️ 处理严重不平衡的标签（考虑重采样或权重调整）")
        
        if self.quality_report.get('consistency', {}).get('smiles_overlaps', {}).get('train_test', 0) > 0:
            suggestions.append("🚫 消除训练集和测试集之间的数据泄露")
        
        diversity_score = self.quality_report.get('molecular_diversity', {}).get('diversity_score')
        if diversity_score and diversity_score < 0.7:
            suggestions.append("🌈 增加分子结构多样性")
        
        if not suggestions:
            suggestions.append("✅ 数据质量良好，可以直接用于训练")
        
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion}")
        
        # 保存建议
        with open('data_improvement_suggestions.txt', 'w', encoding='utf-8') as f:
            f.write("数据质量改进建议\n")
            f.write("=" * 30 + "\n\n")
            for i, suggestion in enumerate(suggestions, 1):
                f.write(f"{i}. {suggestion}\n")
        
        print(f"\n💾 改进建议已保存: data_improvement_suggestions.txt")

def main():
    """主函数"""
    
    enhancer = DataQualityEnhancer()
    quality_report = enhancer.generate_quality_report()
    
    print(f"\n🎯 数据质量分析完成!")
    
    return quality_report

if __name__ == "__main__":
    main()
