#!/usr/bin/env python3
"""
聚焦超参数优化
基于您当前最佳配置(0.7539)进行精细调优
"""

import optuna
import subprocess
import pandas as pd
import numpy as np
import os
import json
import time
from datetime import datetime

class FocusedHyperparameterOptimizer:
    """聚焦超参数优化器"""
    
    def __init__(self):
        self.best_baseline = 0.7539  # 您当前的最佳结果
        self.best_config = {
            'epochs': 80,
            'depth': 5,
            'hidden_dim': 400,
            'batch_size': 24,
            'dropout': 0.2
        }
        self.results = []
        
    def create_combined_dataset(self):
        """创建合并数据集用于训练"""
        if os.path.exists('combined_data.csv'):
            return
            
        train_df = pd.read_csv('train.csv')
        val_df = pd.read_csv('val.csv')
        test_df = pd.read_csv('test.csv')
        
        train_df['split'] = 'train'
        val_df['split'] = 'val'
        test_df['split'] = 'test'
        
        combined_df = pd.concat([train_df, val_df, test_df], ignore_index=True)
        combined_df.to_csv('combined_data.csv', index=False)
        print("✅ 合并数据集创建完成")
    
    def objective(self, trial):
        """Optuna优化目标函数"""
        
        # 基于您最佳配置的邻域搜索
        params = {
            'epochs': trial.suggest_int('epochs', 60, 120),  # 围绕80
            'depth': trial.suggest_int('depth', 4, 7),       # 围绕5
            'hidden_dim': trial.suggest_int('hidden_dim', 300, 600, step=50),  # 围绕400
            'batch_size': trial.suggest_categorical('batch_size', [16, 20, 24, 28, 32]),  # 围绕24
            'dropout': trial.suggest_float('dropout', 0.1, 0.3, step=0.05),  # 围绕0.2
            
            # 学习率精细调优
            'init_lr': trial.suggest_float('init_lr', 1e-6, 1e-4, log=True),
            'max_lr': trial.suggest_float('max_lr', 5e-5, 5e-4, log=True),
            'final_lr': trial.suggest_float('final_lr', 1e-7, 1e-5, log=True),
            'warmup_epochs': trial.suggest_int('warmup_epochs', 3, 10),
            
            # 正则化参数
            'weight_decay': trial.suggest_float('weight_decay', 0, 1e-4, log=True),
        }
        
        # 确保学习率顺序正确
        if params['max_lr'] <= params['init_lr']:
            params['max_lr'] = params['init_lr'] * 2
        if params['final_lr'] >= params['max_lr']:
            params['final_lr'] = params['max_lr'] / 10
        
        return self.train_and_evaluate(params, trial.number)
    
    def train_and_evaluate(self, params, trial_num):
        """训练并评估模型"""
        
        output_dir = f'hyperopt_trial_{trial_num}'
        
        cmd = [
            'chemprop', 'train',
            '--data-path', 'combined_data.csv',
            '--splits-column', 'split',
            '--task-type', 'classification',
            '--output-dir', output_dir,
            '--epochs', str(params['epochs']),
            '--batch-size', str(params['batch_size']),
            '--message-hidden-dim', str(params['hidden_dim']),
            '--depth', str(params['depth']),
            '--dropout', str(params['dropout']),
            '--init-lr', str(params['init_lr']),
            '--max-lr', str(params['max_lr']),
            '--final-lr', str(params['final_lr']),
            '--warmup-epochs', str(params['warmup_epochs']),
            '--weight-decay', str(params['weight_decay']),
            '--data-seed', '42',
            '--save-smiles-splits'
        ]
        
        try:
            print(f"🚀 Trial {trial_num}: 训练中...")
            start_time = time.time()
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
            
            training_time = time.time() - start_time
            
            if result.returncode == 0:
                # 评估模型
                auc_score = self.evaluate_model(output_dir)
                
                if auc_score is not None:
                    improvement = (auc_score - self.best_baseline) / self.best_baseline * 100
                    
                    print(f"✅ Trial {trial_num}: AUC={auc_score:.4f} ({improvement:+.2f}%) 用时={training_time:.1f}s")
                    
                    # 记录结果
                    self.results.append({
                        'trial': trial_num,
                        'params': params,
                        'auc': auc_score,
                        'improvement': improvement,
                        'training_time': training_time
                    })
                    
                    return auc_score
                else:
                    print(f"❌ Trial {trial_num}: 评估失败")
                    return 0.5
            else:
                print(f"❌ Trial {trial_num}: 训练失败")
                return 0.5
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Trial {trial_num}: 训练超时")
            return 0.5
        except Exception as e:
            print(f"❌ Trial {trial_num}: 异常 {e}")
            return 0.5
    
    def evaluate_model(self, model_dir):
        """评估模型性能"""
        
        pred_path = f"{model_dir}_predictions.csv"
        
        cmd = [
            'chemprop', 'predict',
            '--test-path', 'combined_data.csv',
            '--model-paths', model_dir,
            '--preds-path', pred_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and os.path.exists(pred_path):
                # 计算测试集AUC
                test_df = pd.read_csv('combined_data.csv')
                pred_df = pd.read_csv(pred_path)
                
                test_mask = test_df['split'] == 'test'
                test_data = test_df[test_mask]
                test_preds = pred_df[test_mask]
                
                label_cols = [col for col in test_data.columns if col not in ['smiles', 'split']]
                
                from sklearn.metrics import roc_auc_score
                auc_scores = []
                
                for col in label_cols:
                    if col in test_preds.columns:
                        try:
                            y_true = test_data[col].values
                            y_pred = test_preds[col].values
                            
                            if len(np.unique(y_true)) > 1:
                                auc = roc_auc_score(y_true, y_pred)
                                auc_scores.append(auc)
                        except:
                            continue
                
                if auc_scores:
                    return np.mean(auc_scores)
                    
            return None
            
        except Exception as e:
            print(f"评估异常: {e}")
            return None
    
    def optimize(self, n_trials=25):
        """执行超参数优化"""
        
        print("🔧 开始聚焦超参数优化")
        print(f"🎯 目标: 超越当前最佳 {self.best_baseline:.4f}")
        print("=" * 60)
        
        # 准备数据
        self.create_combined_dataset()
        
        # 创建Optuna研究
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=42),
            pruner=optuna.pruners.MedianPruner(n_startup_trials=5)
        )
        
        # 执行优化
        study.optimize(self.objective, n_trials=n_trials)
        
        # 分析结果
        self.analyze_results(study)
        
        return study.best_params, study.best_value
    
    def analyze_results(self, study):
        """分析优化结果"""
        
        print("\n📊 优化结果分析")
        print("=" * 60)
        
        best_params = study.best_params
        best_value = study.best_value
        improvement = (best_value - self.best_baseline) / self.best_baseline * 100
        
        print(f"🏆 最佳结果:")
        print(f"   ROC-AUC: {best_value:.4f}")
        print(f"   改进幅度: {improvement:+.2f}%")
        
        if improvement > 0:
            print(f"🎉 成功超越基线！")
        else:
            print(f"📈 未超越基线，但获得了有价值的参数洞察")
        
        print(f"\n🔧 最佳参数配置:")
        for param, value in best_params.items():
            print(f"   {param}: {value}")
        
        # 保存结果
        results_summary = {
            'optimization_type': 'focused_hyperparameter',
            'baseline_auc': self.best_baseline,
            'best_auc': best_value,
            'improvement_percent': improvement,
            'best_params': best_params,
            'all_trials': self.results,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('focused_hyperopt_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细结果已保存: focused_hyperopt_results.json")
        
        # 参数重要性分析
        if len(study.trials) > 10:
            print(f"\n📈 参数重要性分析:")
            importance = optuna.importance.get_param_importances(study)
            for param, imp in sorted(importance.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"   {param}: {imp:.3f}")

def main():
    """主函数"""
    
    optimizer = FocusedHyperparameterOptimizer()
    best_params, best_score = optimizer.optimize(n_trials=20)  # 适中的试验次数
    
    print(f"\n🎯 超参数优化完成!")
    print(f"最佳AUC: {best_score:.4f}")
    
    return best_params, best_score

if __name__ == "__main__":
    main()
