#!/usr/bin/env python3
"""
集成学习和模型融合策略
通过多模型集成提升预测性能
"""

import pandas as pd
import numpy as np
import os
import subprocess
import json
from sklearn.metrics import roc_auc_score, accuracy_score
from sklearn.ensemble import VotingClassifier
import pickle

class ChemPropEnsemble:
    """ChemProp集成学习器"""
    
    def __init__(self, base_dir="TEST1"):
        self.base_dir = base_dir
        self.models = []
        self.model_configs = []
        
    def create_diverse_models(self):
        """创建多样化的模型配置"""
        
        # 配置1: 深度网络
        config1 = {
            'name': 'deep_network',
            'depth': 7,
            'hidden_size': 400,
            'dropout': 0.3,
            'epochs': 100,
            'batch_size': 20,
            'lr_schedule': 'cosine'
        }
        
        # 配置2: 宽度网络
        config2 = {
            'name': 'wide_network',
            'depth': 4,
            'hidden_size': 600,
            'dropout': 0.2,
            'epochs': 80,
            'batch_size': 24,
            'lr_schedule': 'linear'
        }
        
        # 配置3: 正则化网络
        config3 = {
            'name': 'regularized_network',
            'depth': 5,
            'hidden_size': 450,
            'dropout': 0.4,
            'epochs': 120,
            'batch_size': 16,
            'lr_schedule': 'exponential'
        }
        
        # 配置4: 快速网络
        config4 = {
            'name': 'fast_network',
            'depth': 3,
            'hidden_size': 500,
            'dropout': 0.15,
            'epochs': 60,
            'batch_size': 32,
            'lr_schedule': 'step'
        }
        
        # 配置5: 平衡网络
        config5 = {
            'name': 'balanced_network',
            'depth': 6,
            'hidden_size': 350,
            'dropout': 0.25,
            'epochs': 90,
            'batch_size': 28,
            'lr_schedule': 'cosine'
        }
        
        return [config1, config2, config3, config4, config5]
    
    def train_single_model(self, config, seed=42):
        """训练单个模型"""
        
        model_dir = f"{self.base_dir}/ensemble_{config['name']}_seed_{seed}"
        os.makedirs(model_dir, exist_ok=True)
        
        # 构建训练命令
        cmd = [
            'chemprop', 'train',
            '--data-path', f'{self.base_dir}/train.csv',
            '--separate-val-path', f'{self.base_dir}/val.csv',
            '--task-type', 'classification',
            '--output-dir', model_dir,
            '--epochs', str(config['epochs']),
            '--batch-size', str(config['batch_size']),
            '--message-hidden-dim', str(config['hidden_size']),
            '--depth', str(config['depth']),
            '--dropout', str(config['dropout']),
            '--seed', str(seed),
            '--save-smiles-splits',
            '--quiet'
        ]
        
        # 添加学习率调度
        if config['lr_schedule'] == 'cosine':
            cmd.extend([
                '--init-lr', '0.00001',
                '--max-lr', '0.0001',
                '--final-lr', '0.000001',
                '--warmup-epochs', '5'
            ])
        elif config['lr_schedule'] == 'linear':
            cmd.extend([
                '--init-lr', '0.00005',
                '--max-lr', '0.0002',
                '--final-lr', '0.00001',
                '--warmup-epochs', '3'
            ])
        elif config['lr_schedule'] == 'exponential':
            cmd.extend([
                '--init-lr', '0.00002',
                '--max-lr', '0.00015',
                '--final-lr', '0.000005',
                '--warmup-epochs', '8'
            ])
        elif config['lr_schedule'] == 'step':
            cmd.extend([
                '--init-lr', '0.00003',
                '--max-lr', '0.0003',
                '--final-lr', '0.00001',
                '--warmup-epochs', '2'
            ])
        
        try:
            print(f"🚀 训练模型: {config['name']} (seed: {seed})")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
            
            if result.returncode == 0:
                print(f"✅ {config['name']} 训练完成")
                return model_dir
            else:
                print(f"❌ {config['name']} 训练失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {config['name']} 训练超时")
            return None
        except Exception as e:
            print(f"❌ {config['name']} 训练异常: {e}")
            return None
    
    def train_ensemble(self, num_seeds=3):
        """训练集成模型"""
        
        configs = self.create_diverse_models()
        trained_models = []
        
        print(f"🎯 开始训练集成模型 ({len(configs)} 种配置 × {num_seeds} 个种子)")
        
        for config in configs:
            for seed in range(42, 42 + num_seeds):
                model_dir = self.train_single_model(config, seed)
                if model_dir:
                    trained_models.append({
                        'config': config,
                        'seed': seed,
                        'model_dir': model_dir
                    })
        
        self.models = trained_models
        print(f"✅ 集成训练完成！成功训练 {len(trained_models)} 个模型")
        
        return trained_models
    
    def predict_ensemble(self, test_data_path, output_path):
        """集成预测"""
        
        if not self.models:
            print("❌ 没有可用的训练模型")
            return None
        
        all_predictions = []
        model_weights = []
        
        print("🔮 开始集成预测...")
        
        for model_info in self.models:
            model_dir = model_info['model_dir']
            pred_path = f"{model_dir}/ensemble_predictions.csv"
            
            # 单模型预测
            cmd = [
                'chemprop', 'predict',
                '--test-path', test_data_path,
                '--checkpoint-dir', model_dir,
                '--preds-path', pred_path
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
                
                if result.returncode == 0 and os.path.exists(pred_path):
                    pred_df = pd.read_csv(pred_path)
                    
                    # 提取预测概率（假设是多标签分类）
                    pred_cols = [col for col in pred_df.columns if col != 'smiles']
                    predictions = pred_df[pred_cols].values
                    
                    all_predictions.append(predictions)
                    
                    # 计算模型权重（基于验证性能）
                    weight = self.calculate_model_weight(model_dir)
                    model_weights.append(weight)
                    
                    print(f"✅ {model_info['config']['name']} 预测完成 (权重: {weight:.3f})")
                    
            except Exception as e:
                print(f"❌ {model_info['config']['name']} 预测失败: {e}")
                continue
        
        if not all_predictions:
            print("❌ 没有成功的预测结果")
            return None
        
        # 加权平均集成
        all_predictions = np.array(all_predictions)
        model_weights = np.array(model_weights)
        model_weights = model_weights / model_weights.sum()  # 归一化权重
        
        # 计算加权平均
        ensemble_predictions = np.average(all_predictions, axis=0, weights=model_weights)
        
        # 保存集成预测结果
        test_df = pd.read_csv(test_data_path)
        result_df = test_df[['smiles']].copy()
        
        # 添加预测列
        pred_cols = [col for col in pd.read_csv(f"{self.models[0]['model_dir']}/ensemble_predictions.csv").columns if col != 'smiles']
        for i, col in enumerate(pred_cols):
            result_df[col] = ensemble_predictions[:, i]
        
        result_df.to_csv(output_path, index=False)
        print(f"💾 集成预测结果已保存: {output_path}")
        
        return ensemble_predictions
    
    def calculate_model_weight(self, model_dir):
        """计算模型权重（基于验证性能）"""
        
        # 尝试读取验证结果
        test_scores_path = os.path.join(model_dir, 'test_scores.csv')
        
        if os.path.exists(test_scores_path):
            try:
                scores_df = pd.read_csv(test_scores_path)
                if 'auc' in scores_df.columns:
                    auc_score = scores_df['auc'].mean()
                    return max(auc_score, 0.5)  # 最小权重0.5
            except:
                pass
        
        # 默认权重
        return 0.7
    
    def evaluate_ensemble(self, test_data_path, true_labels_path):
        """评估集成模型性能"""
        
        # 进行集成预测
        pred_path = f"{self.base_dir}/ensemble_predictions.csv"
        predictions = self.predict_ensemble(test_data_path, pred_path)
        
        if predictions is None:
            return None
        
        # 读取真实标签
        true_df = pd.read_csv(true_labels_path)
        label_cols = [col for col in true_df.columns if col != 'smiles']
        true_labels = true_df[label_cols].values
        
        # 计算性能指标
        results = {}
        
        # 计算每个标签的AUC
        for i, label in enumerate(label_cols):
            try:
                auc = roc_auc_score(true_labels[:, i], predictions[:, i])
                results[f'{label}_auc'] = auc
            except:
                results[f'{label}_auc'] = 0.0
        
        # 计算平均AUC
        auc_scores = [v for k, v in results.items() if k.endswith('_auc')]
        results['mean_auc'] = np.mean(auc_scores)
        
        print(f"🏆 集成模型平均AUC: {results['mean_auc']:.4f}")
        
        # 保存评估结果
        with open(f"{self.base_dir}/ensemble_evaluation.json", 'w') as f:
            json.dump(results, f, indent=2)
        
        return results

def run_ensemble_optimization():
    """运行集成学习优化"""
    
    print("🎯 开始集成学习优化...")
    
    # 创建集成学习器
    ensemble = ChemPropEnsemble("TEST1")
    
    # 训练集成模型
    trained_models = ensemble.train_ensemble(num_seeds=2)  # 减少种子数量以节省时间
    
    if not trained_models:
        print("❌ 集成训练失败")
        return
    
    # 评估集成性能
    results = ensemble.evaluate_ensemble("TEST1/test.csv", "TEST1/test.csv")
    
    if results:
        print(f"\n🏆 集成优化完成！")
        print(f"平均ROC-AUC: {results['mean_auc']:.4f}")
        
        if results['mean_auc'] > 0.7539:
            improvement = (results['mean_auc'] - 0.7539) / 0.7539 * 100
            print(f"🚀 性能提升: +{improvement:.2f}%")
        
        return results
    
    return None

if __name__ == "__main__":
    run_ensemble_optimization()
