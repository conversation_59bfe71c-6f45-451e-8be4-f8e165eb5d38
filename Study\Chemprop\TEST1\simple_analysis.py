import pandas as pd

# 读取数据
df = pd.read_csv('goodscents_V3.csv')

print('数据集形状:', df.shape)
print('列数:', len(df.columns))
print('行数:', len(df))

# 检查前几列
print('\n前5列名称:')
print(df.columns[:5].tolist())

# 检查标签列
label_cols = df.columns[2:]
print(f'\n香气标签数量: {len(label_cols)}')

# 统计最常见的香气特征
label_sums = df[label_cols].sum()
top_10 = label_sums.sort_values(ascending=False).head(10)
print('\n最常见的香气特征:')
for feature, count in top_10.items():
    print(f'{feature}: {count}')

print('\n分析完成!')
