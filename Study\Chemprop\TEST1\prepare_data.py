#!/usr/bin/env python3
"""
简化的数据预处理脚本
将香气分子数据转换为ChemProp格式
"""

import csv
import random

def main():
    print("=== 香气分子数据预处理 ===")
    
    # 读取原始数据
    print("正在读取数据...")
    data = []
    headers = []
    
    with open('goodscents_V3.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        headers = next(reader)
        for row in reader:
            data.append(row)
    
    print(f"数据集大小: {len(data)} 个分子")
    print(f"特征数量: {len(headers)} 列")
    print(f"香气特征数量: {len(headers) - 2}")
    
    # 分析标签分布
    print("\n分析标签分布...")
    label_counts = {}
    for i in range(2, len(headers)):
        label_name = headers[i]
        count = sum(1 for row in data if row[i] == '1')
        label_counts[label_name] = count
    
    # 显示最常见的特征
    sorted_labels = sorted(label_counts.items(), key=lambda x: x[1], reverse=True)
    print("\n最常见的香气特征 (前10个):")
    for label, count in sorted_labels[:10]:
        percentage = (count / len(data)) * 100
        print(f"  {label}: {count} ({percentage:.1f}%)")
    
    # 随机打乱数据
    print("\n正在划分数据集...")
    random.seed(42)
    random.shuffle(data)
    
    # 计算数据集大小
    total_size = len(data)
    train_size = int(total_size * 0.8)
    val_size = int(total_size * 0.1)
    test_size = total_size - train_size - val_size
    
    print(f"数据集划分:")
    print(f"  训练集: {train_size} (80%)")
    print(f"  验证集: {val_size} (10%)")
    print(f"  测试集: {test_size} (10%)")
    
    # 分割数据
    train_data = data[:train_size]
    val_data = data[train_size:train_size + val_size]
    test_data = data[train_size + val_size:]
    
    # 创建ChemProp格式的表头 (smiles + 香气特征标签)
    chemprop_headers = ['smiles'] + headers[2:]
    
    # 保存训练集
    print("\n正在保存数据文件...")
    save_file('train.csv', train_data, chemprop_headers)
    save_file('val.csv', val_data, chemprop_headers)
    save_file('test.csv', test_data, chemprop_headers)
    
    # 保存特征名称
    with open('feature_names.txt', 'w', encoding='utf-8') as f:
        for feature in headers[2:]:
            f.write(f"{feature}\n")
    
    print("文件已创建:")
    print("  - train.csv")
    print("  - val.csv")
    print("  - test.csv")
    print("  - feature_names.txt")
    
    print("\n=== 预处理完成 ===")
    return {
        'total_molecules': len(data),
        'train_size': train_size,
        'val_size': val_size,
        'test_size': test_size,
        'num_features': len(headers) - 2
    }

def save_file(filename, data, headers):
    """保存ChemProp格式的CSV文件"""
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        
        for row in data:
            # ChemProp格式: [SMILES, label1, label2, ...]
            chemprop_row = [row[0]] + row[2:]
            writer.writerow(chemprop_row)

if __name__ == "__main__":
    results = main()
