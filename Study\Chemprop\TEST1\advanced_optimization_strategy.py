#!/usr/bin/env python3
"""
高级ChemProp优化策略
基于D-MPNN算法核心的深度优化方案
目标：将ROC-AUC从0.7539提升至0.80+
"""

import os
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import optuna
from sklearn.model_selection import StratifiedKFold
import subprocess
import json

class AdvancedChemPropOptimizer:
    """高级ChemProp优化器"""
    
    def __init__(self, data_path: str, output_dir: str):
        self.data_path = data_path
        self.output_dir = output_dir
        self.best_params = {}
        self.optimization_history = []
        
    def suggest_advanced_hyperparameters(self, trial):
        """建议高级超参数组合"""
        
        # 1. 网络架构优化
        depth = trial.suggest_int('depth', 4, 8)  # 更深的网络
        hidden_size = trial.suggest_int('hidden_size', 300, 800, step=50)
        
        # 2. 学习率策略优化
        init_lr = trial.suggest_float('init_lr', 1e-6, 1e-4, log=True)
        max_lr = trial.suggest_float('max_lr', 1e-5, 1e-3, log=True)
        final_lr = trial.suggest_float('final_lr', 1e-7, 1e-5, log=True)
        
        # 3. 正则化策略
        dropout = trial.suggest_float('dropout', 0.1, 0.4)
        weight_decay = trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True)
        
        # 4. 训练策略
        epochs = trial.suggest_int('epochs', 60, 120)
        batch_size = trial.suggest_categorical('batch_size', [16, 20, 24, 28, 32])
        warmup_epochs = trial.suggest_int('warmup_epochs', 3, 10)
        
        # 5. 数据增强
        noise_variance = trial.suggest_float('noise_variance', 0.0, 0.1)
        
        # 6. 集成学习参数
        ensemble_size = trial.suggest_int('ensemble_size', 3, 7)
        
        return {
            'depth': depth,
            'hidden_size': hidden_size,
            'init_lr': init_lr,
            'max_lr': max_lr,
            'final_lr': final_lr,
            'dropout': dropout,
            'weight_decay': weight_decay,
            'epochs': epochs,
            'batch_size': batch_size,
            'warmup_epochs': warmup_epochs,
            'noise_variance': noise_variance,
            'ensemble_size': ensemble_size
        }
    
    def train_with_ensemble(self, params: Dict) -> float:
        """使用集成学习训练模型"""
        
        ensemble_scores = []
        
        for seed in range(params['ensemble_size']):
            model_dir = f"{self.output_dir}/ensemble_model_{seed}"
            os.makedirs(model_dir, exist_ok=True)
            
            # 构建训练命令
            cmd = [
                'chemprop', 'train',
                '--data-path', self.data_path,
                '--task-type', 'classification',
                '--output-dir', model_dir,
                '--epochs', str(params['epochs']),
                '--batch-size', str(params['batch_size']),
                '--message-hidden-dim', str(params['hidden_size']),
                '--depth', str(params['depth']),
                '--dropout', str(params['dropout']),
                '--init-lr', str(params['init_lr']),
                '--max-lr', str(params['max_lr']),
                '--final-lr', str(params['final_lr']),
                '--warmup-epochs', str(params['warmup_epochs']),
                '--weight-decay', str(params['weight_decay']),
                '--seed', str(42 + seed),
                '--quiet'
            ]
            
            # 添加数据增强
            if params['noise_variance'] > 0:
                cmd.extend(['--noise-variance', str(params['noise_variance'])])
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
                
                # 读取验证结果
                test_scores_path = os.path.join(model_dir, 'test_scores.csv')
                if os.path.exists(test_scores_path):
                    scores_df = pd.read_csv(test_scores_path)
                    auc_score = scores_df['auc'].mean()
                    ensemble_scores.append(auc_score)
                    
            except Exception as e:
                print(f"训练失败 (seed {seed}): {e}")
                continue
        
        # 返回集成平均分数
        return np.mean(ensemble_scores) if ensemble_scores else 0.0
    
    def optimize_with_optuna(self, n_trials: int = 50):
        """使用Optuna进行高级超参数优化"""
        
        def objective(trial):
            params = self.suggest_advanced_hyperparameters(trial)
            score = self.train_with_ensemble(params)
            
            # 记录优化历史
            self.optimization_history.append({
                'trial': trial.number,
                'params': params,
                'score': score
            })
            
            return score
        
        # 创建Optuna研究
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=42),
            pruner=optuna.pruners.MedianPruner()
        )
        
        # 执行优化
        study.optimize(objective, n_trials=n_trials)
        
        # 保存最佳参数
        self.best_params = study.best_params
        
        return study.best_value, study.best_params

class MolecularFeatureEngineer:
    """分子特征工程器"""
    
    @staticmethod
    def generate_advanced_features(smiles_list: List[str]) -> pd.DataFrame:
        """生成高级分子特征"""
        from rdkit import Chem
        from rdkit.Chem import Descriptors, Crippen, Lipinski
        from rdkit.Chem.rdMolDescriptors import CalcNumRotatableBonds
        
        features = []
        
        for smiles in smiles_list:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                features.append([0] * 15)  # 默认特征
                continue
            
            # 基础物理化学性质
            mw = Descriptors.MolWt(mol)
            logp = Crippen.MolLogP(mol)
            hbd = Lipinski.NumHDonors(mol)
            hba = Lipinski.NumHAcceptors(mol)
            rotatable_bonds = CalcNumRotatableBonds(mol)
            
            # 拓扑特征
            num_atoms = mol.GetNumAtoms()
            num_bonds = mol.GetNumBonds()
            num_rings = Descriptors.RingCount(mol)
            aromatic_rings = Descriptors.NumAromaticRings(mol)
            
            # 表面积和体积
            tpsa = Descriptors.TPSA(mol)
            
            # 电子特征
            num_heteroatoms = Descriptors.NumHeteroatoms(mol)
            
            # 复杂度指标
            bertz_ct = Descriptors.BertzCT(mol)
            
            # 香气相关特征（基于分子结构）
            has_ester = len(mol.GetSubstructMatches(Chem.MolFromSmarts('[CX3](=O)[OX2]'))) > 0
            has_aldehyde = len(mol.GetSubstructMatches(Chem.MolFromSmarts('[CX3H1](=O)'))) > 0
            has_alcohol = len(mol.GetSubstructMatches(Chem.MolFromSmarts('[OX2H]'))) > 0
            
            feature_row = [
                mw, logp, hbd, hba, rotatable_bonds,
                num_atoms, num_bonds, num_rings, aromatic_rings,
                tpsa, num_heteroatoms, bertz_ct,
                int(has_ester), int(has_aldehyde), int(has_alcohol)
            ]
            
            features.append(feature_row)
        
        feature_names = [
            'molecular_weight', 'logp', 'hbd', 'hba', 'rotatable_bonds',
            'num_atoms', 'num_bonds', 'num_rings', 'aromatic_rings',
            'tpsa', 'num_heteroatoms', 'bertz_ct',
            'has_ester', 'has_aldehyde', 'has_alcohol'
        ]
        
        return pd.DataFrame(features, columns=feature_names)

def run_advanced_optimization():
    """运行高级优化流程"""
    
    # 配置路径
    data_path = "TEST1/train.csv"
    output_dir = "TEST1/advanced_optimization"
    
    # 创建优化器
    optimizer = AdvancedChemPropOptimizer(data_path, output_dir)
    
    print("🚀 开始高级超参数优化...")
    print("目标：将ROC-AUC从0.7539提升至0.80+")
    
    # 执行优化
    best_score, best_params = optimizer.optimize_with_optuna(n_trials=30)
    
    print(f"\n🏆 优化完成！")
    print(f"最佳ROC-AUC: {best_score:.4f}")
    print(f"最佳参数: {best_params}")
    
    # 保存结果
    results = {
        'best_score': best_score,
        'best_params': best_params,
        'optimization_history': optimizer.optimization_history
    }
    
    with open(f"{output_dir}/optimization_results.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    return best_score, best_params

if __name__ == "__main__":
    run_advanced_optimization()
