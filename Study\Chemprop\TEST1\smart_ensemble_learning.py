#!/usr/bin/env python3
"""
智能集成学习
基于您的最佳配置创建多样化的高质量集成模型
"""

import pandas as pd
import numpy as np
import os
import subprocess
import json
import time
from datetime import datetime
from sklearn.metrics import roc_auc_score

class SmartEnsembleLearning:
    """智能集成学习器"""
    
    def __init__(self):
        # 您的最佳基线配置
        self.best_config = {
            'epochs': 80,
            'depth': 5,
            'hidden_dim': 400,
            'batch_size': 24,
            'dropout': 0.2
        }
        self.models = []
        self.results = {}
        
    def create_combined_dataset(self):
        """创建合并数据集"""
        if os.path.exists('ensemble_data.csv'):
            return
            
        train_df = pd.read_csv('train.csv')
        val_df = pd.read_csv('val.csv')
        test_df = pd.read_csv('test.csv')
        
        train_df['split'] = 'train'
        val_df['split'] = 'val'
        test_df['split'] = 'test'
        
        combined_df = pd.concat([train_df, val_df, test_df], ignore_index=True)
        combined_df.to_csv('ensemble_data.csv', index=False)
        print("✅ 集成数据集创建完成")
    
    def create_diverse_configs(self):
        """创建多样化的模型配置"""
        
        configs = []
        
        # 配置1: 深度优化 (基于您的最佳配置)
        configs.append({
            'name': 'deep_optimized',
            'epochs': 80,
            'depth': 6,  # 稍微增加深度
            'hidden_dim': 400,
            'batch_size': 24,
            'dropout': 0.25,
            'init_lr': 0.00001,
            'max_lr': 0.0001,
            'final_lr': 0.000005,
            'warmup_epochs': 5
        })
        
        # 配置2: 宽度优化
        configs.append({
            'name': 'wide_optimized',
            'epochs': 80,
            'depth': 4,
            'hidden_dim': 500,  # 增加宽度
            'batch_size': 20,   # 调整批次大小
            'dropout': 0.2,
            'init_lr': 0.000008,
            'max_lr': 0.00008,
            'final_lr': 0.000004,
            'warmup_epochs': 6
        })
        
        # 配置3: 正则化强化
        configs.append({
            'name': 'regularized',
            'epochs': 100,  # 更多轮次
            'depth': 5,
            'hidden_dim': 350,
            'batch_size': 28,
            'dropout': 0.3,  # 更强正则化
            'init_lr': 0.000005,
            'max_lr': 0.00005,
            'final_lr': 0.000002,
            'warmup_epochs': 8
        })
        
        # 配置4: 快速收敛
        configs.append({
            'name': 'fast_converge',
            'epochs': 60,
            'depth': 4,
            'hidden_dim': 450,
            'batch_size': 32,
            'dropout': 0.15,
            'init_lr': 0.00002,
            'max_lr': 0.0002,
            'final_lr': 0.00001,
            'warmup_epochs': 3
        })
        
        # 配置5: 平衡配置 (您的最佳配置的变体)
        configs.append({
            'name': 'balanced_variant',
            'epochs': 85,
            'depth': 5,
            'hidden_dim': 380,
            'batch_size': 26,
            'dropout': 0.22,
            'init_lr': 0.000012,
            'max_lr': 0.00012,
            'final_lr': 0.000006,
            'warmup_epochs': 4
        })
        
        return configs
    
    def train_single_model(self, config, seed):
        """训练单个模型"""
        
        model_name = f"ensemble_{config['name']}_seed_{seed}"
        output_dir = f"ensemble_models/{model_name}"
        
        os.makedirs("ensemble_models", exist_ok=True)
        
        cmd = [
            'chemprop', 'train',
            '--data-path', 'ensemble_data.csv',
            '--splits-column', 'split',
            '--task-type', 'classification',
            '--output-dir', output_dir,
            '--epochs', str(config['epochs']),
            '--batch-size', str(config['batch_size']),
            '--message-hidden-dim', str(config['hidden_dim']),
            '--depth', str(config['depth']),
            '--dropout', str(config['dropout']),
            '--init-lr', str(config['init_lr']),
            '--max-lr', str(config['max_lr']),
            '--final-lr', str(config['final_lr']),
            '--warmup-epochs', str(config['warmup_epochs']),
            '--data-seed', str(seed),
            '--save-smiles-splits'
        ]
        
        try:
            print(f"🚀 训练 {model_name}...")
            start_time = time.time()
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
            
            training_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ {model_name} 训练完成 (用时: {training_time:.1f}s)")
                return {
                    'name': model_name,
                    'config': config,
                    'seed': seed,
                    'output_dir': output_dir,
                    'training_time': training_time,
                    'success': True
                }
            else:
                print(f"❌ {model_name} 训练失败")
                return {
                    'name': model_name,
                    'config': config,
                    'seed': seed,
                    'success': False
                }
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {model_name} 训练超时")
            return {'name': model_name, 'success': False}
        except Exception as e:
            print(f"❌ {model_name} 训练异常: {e}")
            return {'name': model_name, 'success': False}
    
    def evaluate_single_model(self, model_info):
        """评估单个模型"""
        
        if not model_info['success']:
            return None
            
        pred_path = f"{model_info['output_dir']}_predictions.csv"
        
        cmd = [
            'chemprop', 'predict',
            '--test-path', 'ensemble_data.csv',
            '--model-paths', model_info['output_dir'],
            '--preds-path', pred_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and os.path.exists(pred_path):
                # 计算AUC
                test_df = pd.read_csv('ensemble_data.csv')
                pred_df = pd.read_csv(pred_path)
                
                test_mask = test_df['split'] == 'test'
                test_data = test_df[test_mask]
                test_preds = pred_df[test_mask]
                
                label_cols = [col for col in test_data.columns if col not in ['smiles', 'split']]
                auc_scores = []
                
                for col in label_cols:
                    if col in test_preds.columns:
                        try:
                            y_true = test_data[col].values
                            y_pred = test_preds[col].values
                            
                            if len(np.unique(y_true)) > 1:
                                auc = roc_auc_score(y_true, y_pred)
                                auc_scores.append(auc)
                        except:
                            continue
                
                if auc_scores:
                    mean_auc = np.mean(auc_scores)
                    print(f"📊 {model_info['name']} AUC: {mean_auc:.4f}")
                    return mean_auc, pred_path
                    
            return None, None
            
        except Exception as e:
            print(f"❌ {model_info['name']} 评估失败: {e}")
            return None, None
    
    def create_ensemble_prediction(self, model_predictions):
        """创建集成预测"""
        
        if not model_predictions:
            return None
            
        print("🎯 创建集成预测...")
        
        # 读取所有预测结果
        all_preds = []
        weights = []
        
        for model_name, (auc, pred_path) in model_predictions.items():
            if pred_path and os.path.exists(pred_path):
                pred_df = pd.read_csv(pred_path)
                test_df = pd.read_csv('ensemble_data.csv')
                
                test_mask = test_df['split'] == 'test'
                test_preds = pred_df[test_mask]
                
                label_cols = [col for col in test_preds.columns if col != 'smiles']
                pred_values = test_preds[label_cols].values
                
                all_preds.append(pred_values)
                weights.append(max(auc, 0.5))  # 最小权重0.5
        
        if not all_preds:
            return None
            
        # 加权平均集成
        all_preds = np.array(all_preds)
        weights = np.array(weights)
        weights = weights / weights.sum()  # 归一化
        
        ensemble_pred = np.average(all_preds, axis=0, weights=weights)
        
        # 计算集成AUC
        test_df = pd.read_csv('ensemble_data.csv')
        test_mask = test_df['split'] == 'test'
        test_data = test_df[test_mask]
        
        label_cols = [col for col in test_data.columns if col not in ['smiles', 'split']]
        ensemble_auc_scores = []
        
        for i, col in enumerate(label_cols):
            try:
                y_true = test_data[col].values
                y_pred = ensemble_pred[:, i]
                
                if len(np.unique(y_true)) > 1:
                    auc = roc_auc_score(y_true, y_pred)
                    ensemble_auc_scores.append(auc)
            except:
                continue
        
        if ensemble_auc_scores:
            ensemble_auc = np.mean(ensemble_auc_scores)
            print(f"🏆 集成模型 AUC: {ensemble_auc:.4f}")
            return ensemble_auc
            
        return None
    
    def run_ensemble_learning(self, num_seeds=2):
        """运行集成学习"""
        
        print("🎯 开始智能集成学习")
        print("=" * 60)
        
        # 准备数据
        self.create_combined_dataset()
        
        # 获取多样化配置
        configs = self.create_diverse_configs()
        
        print(f"📋 将训练 {len(configs)} 种配置 × {num_seeds} 个种子 = {len(configs) * num_seeds} 个模型")
        
        # 训练所有模型
        trained_models = []
        
        for config in configs:
            for seed in range(42, 42 + num_seeds):
                model_info = self.train_single_model(config, seed)
                trained_models.append(model_info)
        
        # 评估所有模型
        print(f"\n📊 评估模型性能...")
        model_predictions = {}
        individual_results = []
        
        for model_info in trained_models:
            if model_info['success']:
                auc, pred_path = self.evaluate_single_model(model_info)
                if auc is not None:
                    model_predictions[model_info['name']] = (auc, pred_path)
                    individual_results.append({
                        'name': model_info['name'],
                        'config': model_info['config']['name'],
                        'seed': model_info['seed'],
                        'auc': auc,
                        'training_time': model_info['training_time']
                    })
        
        # 创建集成预测
        ensemble_auc = self.create_ensemble_prediction(model_predictions)
        
        # 分析结果
        self.analyze_ensemble_results(individual_results, ensemble_auc)
        
        return ensemble_auc, individual_results
    
    def analyze_ensemble_results(self, individual_results, ensemble_auc):
        """分析集成结果"""
        
        print(f"\n📊 集成学习结果分析")
        print("=" * 60)
        
        if individual_results:
            individual_aucs = [r['auc'] for r in individual_results]
            best_individual = max(individual_aucs)
            mean_individual = np.mean(individual_aucs)
            
            print(f"📈 个体模型性能:")
            print(f"   最佳个体 AUC: {best_individual:.4f}")
            print(f"   平均个体 AUC: {mean_individual:.4f}")
            print(f"   成功训练模型数: {len(individual_results)}")
            
            if ensemble_auc:
                ensemble_improvement = (ensemble_auc - best_individual) / best_individual * 100
                baseline_improvement = (ensemble_auc - 0.7539) / 0.7539 * 100
                
                print(f"\n🏆 集成模型性能:")
                print(f"   集成 AUC: {ensemble_auc:.4f}")
                print(f"   vs 最佳个体: {ensemble_improvement:+.2f}%")
                print(f"   vs 基线(0.7539): {baseline_improvement:+.2f}%")
                
                if ensemble_auc > 0.7539:
                    print(f"🎉 成功超越基线！")
                else:
                    print(f"📈 未超越基线，但集成学习展现了潜力")
        
        # 保存结果
        results_summary = {
            'ensemble_type': 'smart_ensemble',
            'baseline_auc': 0.7539,
            'ensemble_auc': ensemble_auc,
            'individual_results': individual_results,
            'best_individual_auc': max([r['auc'] for r in individual_results]) if individual_results else None,
            'mean_individual_auc': np.mean([r['auc'] for r in individual_results]) if individual_results else None,
            'improvement_vs_baseline': (ensemble_auc - 0.7539) / 0.7539 * 100 if ensemble_auc else None,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('smart_ensemble_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 集成结果已保存: smart_ensemble_results.json")

def main():
    """主函数"""
    
    ensemble = SmartEnsembleLearning()
    ensemble_auc, individual_results = ensemble.run_ensemble_learning(num_seeds=2)
    
    print(f"\n🎯 集成学习完成!")
    if ensemble_auc:
        print(f"集成AUC: {ensemble_auc:.4f}")
    
    return ensemble_auc

if __name__ == "__main__":
    main()
