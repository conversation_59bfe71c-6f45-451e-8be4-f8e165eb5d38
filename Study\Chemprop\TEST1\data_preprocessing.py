#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香气分子数据预处理脚本
将数据转换为ChemProp所需的格式
"""

import csv
import random
from pathlib import Path

def load_and_analyze_data():
    """加载并分析原始数据"""
    print("正在加载数据...")
    
    data = []
    with open('goodscents_V3.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        headers = next(reader)  # 读取表头
        
        for row in reader:
            data.append(row)
    
    print(f"数据集大小: {len(data)} 个分子")
    print(f"特征数量: {len(headers)} 列")
    print(f"SMILES列: {headers[0]}")
    print(f"描述列: {headers[1]}")
    print(f"香气特征数量: {len(headers) - 2}")
    
    # 分析标签分布
    label_counts = {}
    for i in range(2, len(headers)):
        label_name = headers[i]
        count = sum(1 for row in data if row[i] == '1')
        label_counts[label_name] = count
    
    # 显示最常见的特征
    sorted_labels = sorted(label_counts.items(), key=lambda x: x[1], reverse=True)
    print("\n最常见的香气特征 (前10个):")
    for label, count in sorted_labels[:10]:
        percentage = (count / len(data)) * 100
        print(f"  {label}: {count} ({percentage:.1f}%)")
    
    return data, headers, label_counts

def create_chemprop_format(data, headers, train_ratio=0.8, val_ratio=0.1):
    """
    创建ChemProp格式的数据文件
    
    Args:
        data: 原始数据
        headers: 列名
        train_ratio: 训练集比例
        val_ratio: 验证集比例
    """
    print("\n正在创建ChemProp格式数据...")
    
    # 随机打乱数据
    random.seed(42)  # 设置随机种子以确保可重复性
    random.shuffle(data)
    
    # 计算数据集大小
    total_size = len(data)
    train_size = int(total_size * train_ratio)
    val_size = int(total_size * val_ratio)
    test_size = total_size - train_size - val_size
    
    print(f"数据集划分:")
    print(f"  训练集: {train_size} ({train_ratio*100:.0f}%)")
    print(f"  验证集: {val_size} ({val_ratio*100:.0f}%)")
    print(f"  测试集: {test_size} ({(1-train_ratio-val_ratio)*100:.0f}%)")
    
    # 分割数据
    train_data = data[:train_size]
    val_data = data[train_size:train_size + val_size]
    test_data = data[train_size + val_size:]
    
    # 创建ChemProp格式的表头
    # ChemProp格式: smiles,target1,target2,...
    chemprop_headers = ['smiles'] + headers[2:]  # SMILES + 香气特征标签
    
    # 保存训练集
    save_chemprop_file('train.csv', train_data, chemprop_headers, headers)
    save_chemprop_file('val.csv', val_data, chemprop_headers, headers)
    save_chemprop_file('test.csv', test_data, chemprop_headers, headers)
    
    print("ChemProp格式数据文件已创建:")
    print("  - train.csv")
    print("  - val.csv") 
    print("  - test.csv")
    
    return train_size, val_size, test_size

def save_chemprop_file(filename, data, chemprop_headers, original_headers):
    """保存ChemProp格式的CSV文件"""
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # 写入表头
        writer.writerow(chemprop_headers)
        
        # 写入数据
        for row in data:
            # 构建ChemProp格式的行: [SMILES, label1, label2, ...]
            chemprop_row = [row[0]] + row[2:]  # SMILES + 标签列
            writer.writerow(chemprop_row)

def create_features_info():
    """创建特征信息文件，用于后续分析"""
    print("\n正在创建特征信息文件...")
    
    # 读取表头
    with open('goodscents_V3.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        headers = next(reader)
    
    # 保存特征名称
    feature_names = headers[2:]  # 香气特征名称
    
    with open('feature_names.txt', 'w', encoding='utf-8') as f:
        for feature in feature_names:
            f.write(f"{feature}\n")
    
    print(f"特征信息已保存到 feature_names.txt ({len(feature_names)} 个特征)")

def main():
    """主函数"""
    print("=== 香气分子数据预处理 ===")
    
    # 加载和分析数据
    data, headers, label_counts = load_and_analyze_data()
    
    # 创建ChemProp格式数据
    train_size, val_size, test_size = create_chemprop_format(data, headers)
    
    # 创建特征信息文件
    create_features_info()
    
    print("\n=== 预处理完成 ===")
    print("数据已准备好用于ChemProp训练!")
    
    return {
        'total_molecules': len(data),
        'train_size': train_size,
        'val_size': val_size,
        'test_size': test_size,
        'num_features': len(headers) - 2,
        'label_counts': label_counts
    }

if __name__ == "__main__":
    results = main()
