#!/usr/bin/env python3
"""
分子特征增强策略
基于香气分子的化学结构特点进行特征工程
"""

import pandas as pd
import numpy as np
from rdkit import Chem
from rdkit.Chem import Descriptors, Fragments, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from rdkit.Chem.rdMolDescriptors import CalcNumRotatableBonds, CalcTPSA
from rdkit.Chem import rdMolDescriptors
import subprocess
import os

class FragranceFeatureEngineer:
    """香气分子特征工程器"""
    
    def __init__(self):
        # 香气相关的SMARTS模式
        self.fragrance_patterns = {
            # 果香基团
            'ester_fruity': '[CX3](=O)[OX2][CX4]',
            'acetate': '[CX3](=O)[OX2][CX4][CX4]',
            'butyrate': '[CX3](=O)[OX2][CX4][CX4][CX4]',
            
            # 花香基团
            'benzyl_alcohol': 'c1ccc(CO)cc1',
            'phenylethyl_alcohol': 'c1ccc(CCO)cc1',
            'linalool_like': '[CX4][CX3]([CX4])([CX4])[CX4][CX4][CX3]([CX4])=[CX3]',
            
            # 绿叶香基团
            'aldehyde_green': '[CX3H1](=O)[CX4][CX4]',
            'hexenal_like': '[CX3H1](=O)[CX4][CX4][CX3]=[CX3]',
            
            # 甜香基团
            'vanillin_like': 'c1cc(OC)c(O)cc1[CX3H1]=O',
            'coumarin_like': 'c1ccc2oc(=O)ccc2c1',
            
            # 木香基团
            'sandalwood_like': '[CX4][CX4][CX4][CX4][CX4][CX4][CX4][CX4][CX4][CX4][CX4][CX4]',
            'cedar_like': 'C1CC2CCC1C2',
            
            # 柑橘香基团
            'limonene_like': 'C1CC(=CCC1C(C)C)C',
            'citral_like': '[CX3H1](=O)[CX4][CX4][CX3]([CX4])=[CX3][CX4][CX4][CX3]([CX4])=[CX3]',
            
            # 麝香基团
            'musk_macrocycle': '[CX4]1[CX4][CX4][CX4][CX4][CX4][CX4][CX4][CX4][CX4][CX4][CX4]1',
            'nitro_musk': '[NX3+](=O)[O-]',
        }
    
    def extract_fragrance_features(self, smiles_list):
        """提取香气相关特征"""
        features = []
        
        for smiles in smiles_list:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                features.append([0] * len(self.fragrance_patterns))
                continue
            
            # 检测香气模式
            pattern_counts = []
            for pattern_name, smarts in self.fragrance_patterns.items():
                pattern = Chem.MolFromSmarts(smarts)
                if pattern:
                    matches = mol.GetSubstructMatches(pattern)
                    pattern_counts.append(len(matches))
                else:
                    pattern_counts.append(0)
            
            features.append(pattern_counts)
        
        feature_names = list(self.fragrance_patterns.keys())
        return pd.DataFrame(features, columns=feature_names)
    
    def extract_advanced_descriptors(self, smiles_list):
        """提取高级分子描述符"""
        features = []
        
        for smiles in smiles_list:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                features.append([0] * 25)
                continue
            
            # 基础物理化学性质
            mw = Descriptors.MolWt(mol)
            logp = Crippen.MolLogP(mol)
            hbd = Lipinski.NumHDonors(mol)
            hba = Lipinski.NumHAcceptors(mol)
            rotatable_bonds = CalcNumRotatableBonds(mol)
            tpsa = CalcTPSA(mol)
            
            # 拓扑指数
            kappa1 = rdMolDescriptors.Kappa1(mol)
            kappa2 = rdMolDescriptors.Kappa2(mol)
            kappa3 = rdMolDescriptors.Kappa3(mol)
            
            # 分子形状
            asphericity = rdMolDescriptors.Asphericity(mol)
            eccentricity = rdMolDescriptors.Eccentricity(mol)
            
            # 电子特征
            num_valence_electrons = Descriptors.NumValenceElectrons(mol)
            num_radical_electrons = Descriptors.NumRadicalElectrons(mol)
            
            # 芳香性特征
            num_aromatic_rings = Descriptors.NumAromaticRings(mol)
            num_saturated_rings = Descriptors.NumSaturatedRings(mol)
            num_aliphatic_rings = Descriptors.NumAliphaticRings(mol)
            
            # 杂原子特征
            num_heteroatoms = Descriptors.NumHeteroatoms(mol)
            fraction_csp3 = rdMolDescriptors.CalcFractionCsp3(mol)
            
            # 复杂度指标
            bertz_ct = Descriptors.BertzCT(mol)
            
            # 香气强度相关特征
            volatility_index = self.calculate_volatility_index(mol)
            hydrophobicity_index = self.calculate_hydrophobicity_index(mol)
            
            # 分子柔性
            flexibility_index = rotatable_bonds / max(mol.GetNumBonds(), 1)
            
            # 分子紧密度
            compactness = mol.GetNumAtoms() / max(mw, 1) * 100
            
            # 极性表面积比例
            psa_ratio = tpsa / max(mw, 1) * 100
            
            # 芳香性比例
            aromatic_ratio = num_aromatic_rings / max(Descriptors.RingCount(mol), 1) if Descriptors.RingCount(mol) > 0 else 0
            
            feature_row = [
                mw, logp, hbd, hba, rotatable_bonds, tpsa,
                kappa1, kappa2, kappa3, asphericity, eccentricity,
                num_valence_electrons, num_radical_electrons,
                num_aromatic_rings, num_saturated_rings, num_aliphatic_rings,
                num_heteroatoms, fraction_csp3, bertz_ct,
                volatility_index, hydrophobicity_index, flexibility_index,
                compactness, psa_ratio, aromatic_ratio
            ]
            
            features.append(feature_row)
        
        feature_names = [
            'mw', 'logp', 'hbd', 'hba', 'rotatable_bonds', 'tpsa',
            'kappa1', 'kappa2', 'kappa3', 'asphericity', 'eccentricity',
            'num_valence_electrons', 'num_radical_electrons',
            'num_aromatic_rings', 'num_saturated_rings', 'num_aliphatic_rings',
            'num_heteroatoms', 'fraction_csp3', 'bertz_ct',
            'volatility_index', 'hydrophobicity_index', 'flexibility_index',
            'compactness', 'psa_ratio', 'aromatic_ratio'
        ]
        
        return pd.DataFrame(features, columns=feature_names)
    
    def calculate_volatility_index(self, mol):
        """计算挥发性指数（基于分子量和极性）"""
        mw = Descriptors.MolWt(mol)
        tpsa = CalcTPSA(mol)
        
        # 挥发性与分子量负相关，与极性负相关
        volatility = 1000 / (mw + tpsa + 1)
        return volatility
    
    def calculate_hydrophobicity_index(self, mol):
        """计算疏水性指数"""
        logp = Crippen.MolLogP(mol)
        tpsa = CalcTPSA(mol)
        
        # 疏水性指数结合LogP和极性表面积
        hydrophobicity = logp - (tpsa / 100)
        return hydrophobicity
    
    def create_enhanced_dataset(self, input_csv, output_csv):
        """创建增强特征数据集"""
        print("📊 读取原始数据...")
        df = pd.read_csv(input_csv)
        
        print("🧬 提取香气特征...")
        fragrance_features = self.extract_fragrance_features(df['smiles'])
        
        print("🔬 提取高级描述符...")
        advanced_features = self.extract_advanced_descriptors(df['smiles'])
        
        # 合并特征
        enhanced_df = pd.concat([df, fragrance_features, advanced_features], axis=1)
        
        print(f"✅ 特征增强完成！原始特征: {len(df.columns)}, 增强后: {len(enhanced_df.columns)}")
        
        # 保存增强数据集
        enhanced_df.to_csv(output_csv, index=False)
        print(f"💾 增强数据集已保存: {output_csv}")
        
        return enhanced_df

def train_with_enhanced_features():
    """使用增强特征训练模型"""
    
    # 创建特征工程器
    engineer = FragranceFeatureEngineer()
    
    # 增强训练、验证和测试数据
    datasets = ['train.csv', 'val.csv', 'test.csv']
    
    for dataset in datasets:
        input_path = f"TEST1/{dataset}"
        output_path = f"TEST1/enhanced_{dataset}"
        
        if os.path.exists(input_path):
            engineer.create_enhanced_dataset(input_path, output_path)
    
    # 使用增强特征训练模型
    print("\n🚀 开始训练增强特征模型...")
    
    cmd = [
        'chemprop', 'train',
        '--data-path', 'TEST1/enhanced_train.csv',
        '--separate-val-path', 'TEST1/enhanced_val.csv',
        '--separate-test-path', 'TEST1/enhanced_test.csv',
        '--task-type', 'classification',
        '--output-dir', 'TEST1/enhanced_feature_model',
        '--epochs', '100',
        '--batch-size', '24',
        '--message-hidden-dim', '500',
        '--depth', '6',
        '--dropout', '0.25',
        '--init-lr', '0.00001',
        '--max-lr', '0.0001',
        '--final-lr', '0.000005',
        '--warmup-epochs', '8',
        '--ensemble-size', '5',
        '--save-smiles-splits'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=7200)
        print("✅ 训练完成！")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ 警告信息:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("❌ 训练超时")
    except Exception as e:
        print(f"❌ 训练失败: {e}")

if __name__ == "__main__":
    train_with_enhanced_features()
