#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香气分子多标签分类模型训练脚本
使用ChemProp进行香气特征预测
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def run_command(cmd, description):
    """运行命令并处理输出"""
    print(f"\n🚀 {description}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            check=True,
            cwd=Path.cwd()
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ {description} 成功完成!")
        print(f"⏱️  耗时: {duration:.1f}秒")
        
        if result.stdout:
            print(f"\n📊 输出:")
            print(result.stdout)
            
        return True, result.stdout
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"❌ {description} 失败!")
        print(f"⏱️  耗时: {duration:.1f}秒")
        print(f"错误代码: {e.returncode}")
        
        if e.stdout:
            print(f"\n标准输出:")
            print(e.stdout)
        if e.stderr:
            print(f"\n错误输出:")
            print(e.stderr)
            
        return False, e.stderr

def train_fragrance_model():
    """训练香气分子多标签分类模型"""
    print("=== 香气分子多标签分类模型训练 ===")
    
    # 检查数据文件是否存在
    required_files = ['train.csv', 'val.csv', 'test.csv']
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少数据文件: {file}")
            return False
    
    print("✅ 数据文件检查通过")
    
    # 创建模型输出目录
    model_dir = "fragrance_model"
    Path(model_dir).mkdir(exist_ok=True)
    
    # 构建训练命令 - 多标签分类配置
    cmd = [
        "chemprop", "train",
        "--data-path", "train.csv",
        "--separate-val-path", "val.csv",  # 使用单独的验证集
        "--separate-test-path", "test.csv",  # 使用单独的测试集
        "--task-type", "classification",  # 分类任务
        "--output-dir", model_dir,
        "--epochs", "50",  # 增加训练轮数
        "--batch-size", "32",  # 适中的批次大小
        "--message-hidden-dim", "300",  # 消息传递隐藏层维度
        "--depth", "4",  # 消息传递层数
        "--dropout", "0.2",  # Dropout防止过拟合
        "--data-seed", "42",  # 数据种子
        "--pytorch-seed", "42",  # PyTorch种子
        "--save-smiles-splits",  # 保存数据分割信息
        "--num-workers", "0",  # 避免多进程问题
        "--quiet"  # 减少输出噪音
    ]
    
    # 执行训练
    success, output = run_command(cmd, "训练香气分子多标签分类模型")
    
    if success:
        print("\n🎉 模型训练完成!")
        print(f"📁 模型保存在: {model_dir}")
        
        # 检查生成的文件
        model_path = Path(model_dir)
        if (model_path / "model_0").exists():
            print("✅ 模型文件生成成功")
            
            # 列出生成的文件
            print("\n📋 生成的文件:")
            for file in model_path.rglob("*"):
                if file.is_file():
                    print(f"  - {file.relative_to(model_path)}")
        
        return True
    else:
        print("\n❌ 模型训练失败!")
        return False

def predict_test_molecules():
    """使用训练好的模型预测测试集"""
    print("\n=== 预测测试集 ===")
    
    model_path = Path("fragrance_model/model_0")
    if not model_path.exists():
        print("❌ 找不到训练好的模型")
        return False
    
    # 构建预测命令
    cmd = [
        "chemprop", "predict",
        "--test-path", "test.csv",
        "--checkpoint-dir", "fragrance_model",
        "--preds-path", "test_predictions.csv"
    ]
    
    # 执行预测
    success, output = run_command(cmd, "预测测试集香气特征")
    
    if success:
        print("✅ 测试集预测完成!")
        print("📁 预测结果保存在: test_predictions.csv")
        return True
    else:
        print("❌ 测试集预测失败!")
        return False

def analyze_results():
    """分析训练和预测结果"""
    print("\n=== 结果分析 ===")
    
    # 检查预测文件
    pred_file = Path("test_predictions.csv")
    if pred_file.exists():
        print("✅ 找到预测结果文件")
        
        # 简单统计
        with open(pred_file, 'r') as f:
            lines = f.readlines()
            print(f"📊 预测了 {len(lines)-1} 个分子的香气特征")
    
    # 检查模型文件
    model_dir = Path("fragrance_model")
    if model_dir.exists():
        print("✅ 模型训练完成")
        
        # 查找日志文件
        log_files = list(model_dir.rglob("*.log"))
        if log_files:
            print(f"📋 找到 {len(log_files)} 个日志文件")
    
    print("\n🎯 训练总结:")
    print("  - 数据集: 4392个香气分子")
    print("  - 特征数: 138个香气特征")
    print("  - 训练集: 3513个分子")
    print("  - 验证集: 439个分子")
    print("  - 测试集: 440个分子")
    print("  - 任务类型: 多标签分类")
    print("  - 模型架构: 图神经网络 (ChemProp)")

def main():
    """主函数"""
    print("🧪 香气分子多标签分类模型训练")
    print("=" * 60)
    
    # 检查环境
    try:
        result = subprocess.run(["chemprop", "--help"], 
                              capture_output=True, text=True, check=True)
        print("✅ ChemProp 环境检查通过")
    except:
        print("❌ ChemProp 未正确安装或配置")
        return
    
    # 执行训练流程
    if train_fragrance_model():
        if predict_test_molecules():
            analyze_results()
            print("\n🎉 完整训练流程执行成功!")
        else:
            print("\n⚠️  训练成功但预测失败")
    else:
        print("\n❌ 训练流程失败")

if __name__ == "__main__":
    main()
