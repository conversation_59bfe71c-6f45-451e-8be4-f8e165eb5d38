#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化优化实验
解决依赖和编码问题，专注于核心优化
"""

import os
import json
import time
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.metrics import roc_auc_score

class SimplifiedOptimizer:
    """简化优化器"""
    
    def __init__(self):
        self.baseline_auc = 0.7539
        self.results = {}
        
    def create_combined_dataset(self):
        """创建合并数据集"""
        if os.path.exists('simple_combined.csv'):
            return True
            
        try:
            train_df = pd.read_csv('train.csv')
            val_df = pd.read_csv('val.csv')
            test_df = pd.read_csv('test.csv')
            
            train_df['split'] = 'train'
            val_df['split'] = 'val'
            test_df['split'] = 'test'
            
            combined_df = pd.concat([train_df, val_df, test_df], ignore_index=True)
            combined_df.to_csv('simple_combined.csv', index=False)
            
            print("数据集准备完成")
            return True
        except Exception as e:
            print(f"数据集准备失败: {e}")
            return False
    
    def basic_data_analysis(self):
        """基础数据分析"""
        
        print("\n=== 步骤1: 基础数据分析 ===")
        
        try:
            train_df = pd.read_csv('train.csv')
            val_df = pd.read_csv('val.csv')
            test_df = pd.read_csv('test.csv')
            
            print(f"训练集: {len(train_df)} 样本")
            print(f"验证集: {len(val_df)} 样本")
            print(f"测试集: {len(test_df)} 样本")
            
            # 检查标签分布
            label_cols = [col for col in train_df.columns if col != 'smiles']
            print(f"标签数量: {len(label_cols)}")
            
            # 计算平均正例比例
            positive_ratios = []
            for col in label_cols:
                ratio = train_df[col].mean()
                positive_ratios.append(ratio)
            
            mean_positive_ratio = np.mean(positive_ratios)
            print(f"平均正例比例: {mean_positive_ratio:.3f}")
            
            # 检查不平衡标签
            imbalanced_count = sum(1 for ratio in positive_ratios if ratio < 0.05 or ratio > 0.95)
            print(f"严重不平衡标签: {imbalanced_count}")
            
            self.results['data_analysis'] = {
                'total_samples': len(train_df) + len(val_df) + len(test_df),
                'total_labels': len(label_cols),
                'mean_positive_ratio': mean_positive_ratio,
                'imbalanced_labels': imbalanced_count
            }
            
            return True
            
        except Exception as e:
            print(f"数据分析失败: {e}")
            return False
    
    def architecture_quick_test(self):
        """快速架构测试"""
        
        print("\n=== 步骤2: 快速架构测试 ===")
        
        if not self.create_combined_dataset():
            return False
        
        # 测试几个关键架构配置
        configs = [
            {'name': 'baseline', 'depth': 5, 'hidden_dim': 400, 'epochs': 30},
            {'name': 'deeper', 'depth': 6, 'hidden_dim': 400, 'epochs': 30},
            {'name': 'wider', 'depth': 5, 'hidden_dim': 500, 'epochs': 30},
            {'name': 'compact', 'depth': 4, 'hidden_dim': 350, 'epochs': 30}
        ]
        
        arch_results = []
        
        for config in configs:
            print(f"\n测试架构: {config['name']}")
            
            output_dir = f"simple_arch_{config['name']}"
            
            cmd = [
                'chemprop', 'train',
                '--data-path', 'simple_combined.csv',
                '--splits-column', 'split',
                '--task-type', 'classification',
                '--output-dir', output_dir,
                '--epochs', str(config['epochs']),
                '--batch-size', '24',
                '--message-hidden-dim', str(config['hidden_dim']),
                '--depth', str(config['depth']),
                '--dropout', '0.2',
                '--data-seed', '42'
            ]
            
            try:
                start_time = time.time()
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
                training_time = time.time() - start_time
                
                if result.returncode == 0:
                    # 评估模型
                    auc = self.evaluate_model(output_dir)
                    
                    if auc:
                        improvement = (auc - self.baseline_auc) / self.baseline_auc * 100
                        print(f"  AUC: {auc:.4f} ({improvement:+.2f}%)")
                        
                        arch_results.append({
                            'name': config['name'],
                            'config': config,
                            'auc': auc,
                            'improvement': improvement,
                            'training_time': training_time
                        })
                    else:
                        print(f"  评估失败")
                else:
                    print(f"  训练失败")
                    
            except subprocess.TimeoutExpired:
                print(f"  训练超时")
            except Exception as e:
                print(f"  异常: {e}")
        
        if arch_results:
            best_arch = max(arch_results, key=lambda x: x['auc'])
            print(f"\n最佳架构: {best_arch['name']} (AUC: {best_arch['auc']:.4f})")
            
            self.results['architecture'] = {
                'best_config': best_arch,
                'all_results': arch_results
            }
            return True
        else:
            print("所有架构测试失败")
            return False
    
    def hyperparameter_grid_search(self):
        """超参数网格搜索"""
        
        print("\n=== 步骤3: 超参数网格搜索 ===")
        
        if not self.create_combined_dataset():
            return False
        
        # 基于您的最佳配置进行邻域搜索
        param_grid = [
            {'name': 'baseline', 'batch_size': 24, 'dropout': 0.2, 'lr': 0.0001},
            {'name': 'larger_batch', 'batch_size': 32, 'dropout': 0.2, 'lr': 0.0001},
            {'name': 'higher_dropout', 'batch_size': 24, 'dropout': 0.3, 'lr': 0.0001},
            {'name': 'lower_lr', 'batch_size': 24, 'dropout': 0.2, 'lr': 0.00005},
            {'name': 'optimized', 'batch_size': 28, 'dropout': 0.25, 'lr': 0.00008}
        ]
        
        hyper_results = []
        
        for params in param_grid:
            print(f"\n测试参数: {params['name']}")
            
            output_dir = f"simple_hyper_{params['name']}"
            
            cmd = [
                'chemprop', 'train',
                '--data-path', 'simple_combined.csv',
                '--splits-column', 'split',
                '--task-type', 'classification',
                '--output-dir', output_dir,
                '--epochs', '40',  # 稍微增加训练轮数
                '--batch-size', str(params['batch_size']),
                '--message-hidden-dim', '400',
                '--depth', '5',
                '--dropout', str(params['dropout']),
                '--init-lr', str(params['lr']/10),
                '--max-lr', str(params['lr']),
                '--final-lr', str(params['lr']/20),
                '--data-seed', '42'
            ]
            
            try:
                start_time = time.time()
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=2400)
                training_time = time.time() - start_time
                
                if result.returncode == 0:
                    auc = self.evaluate_model(output_dir)
                    
                    if auc:
                        improvement = (auc - self.baseline_auc) / self.baseline_auc * 100
                        print(f"  AUC: {auc:.4f} ({improvement:+.2f}%)")
                        
                        hyper_results.append({
                            'name': params['name'],
                            'params': params,
                            'auc': auc,
                            'improvement': improvement,
                            'training_time': training_time
                        })
                    else:
                        print(f"  评估失败")
                else:
                    print(f"  训练失败")
                    
            except subprocess.TimeoutExpired:
                print(f"  训练超时")
            except Exception as e:
                print(f"  异常: {e}")
        
        if hyper_results:
            best_hyper = max(hyper_results, key=lambda x: x['auc'])
            print(f"\n最佳超参数: {best_hyper['name']} (AUC: {best_hyper['auc']:.4f})")
            
            self.results['hyperparameters'] = {
                'best_config': best_hyper,
                'all_results': hyper_results
            }
            return True
        else:
            print("所有超参数测试失败")
            return False
    
    def simple_ensemble(self):
        """简单集成学习"""
        
        print("\n=== 步骤4: 简单集成学习 ===")
        
        if not self.create_combined_dataset():
            return False
        
        # 训练3个不同种子的模型
        ensemble_models = []
        
        for seed in [42, 123, 456]:
            print(f"\n训练集成模型 (种子: {seed})")
            
            output_dir = f"simple_ensemble_seed_{seed}"
            
            cmd = [
                'chemprop', 'train',
                '--data-path', 'simple_combined.csv',
                '--splits-column', 'split',
                '--task-type', 'classification',
                '--output-dir', output_dir,
                '--epochs', '50',  # 更多轮次
                '--batch-size', '24',
                '--message-hidden-dim', '400',
                '--depth', '5',
                '--dropout', '0.2',
                '--data-seed', str(seed)
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=3000)
                
                if result.returncode == 0:
                    auc = self.evaluate_model(output_dir)
                    if auc:
                        print(f"  个体AUC: {auc:.4f}")
                        ensemble_models.append((output_dir, auc))
                    else:
                        print(f"  评估失败")
                else:
                    print(f"  训练失败")
                    
            except subprocess.TimeoutExpired:
                print(f"  训练超时")
            except Exception as e:
                print(f"  异常: {e}")
        
        # 计算集成预测
        if len(ensemble_models) >= 2:
            ensemble_auc = self.compute_ensemble_auc(ensemble_models)
            
            if ensemble_auc:
                improvement = (ensemble_auc - self.baseline_auc) / self.baseline_auc * 100
                print(f"\n集成AUC: {ensemble_auc:.4f} ({improvement:+.2f}%)")
                
                self.results['ensemble'] = {
                    'ensemble_auc': ensemble_auc,
                    'individual_models': ensemble_models,
                    'improvement': improvement
                }
                return True
        
        print("集成学习失败")
        return False
    
    def evaluate_model(self, model_dir):
        """评估模型"""
        
        pred_path = f"{model_dir}_predictions.csv"
        
        cmd = [
            'chemprop', 'predict',
            '--test-path', 'simple_combined.csv',
            '--model-paths', model_dir,
            '--preds-path', pred_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and os.path.exists(pred_path):
                # 计算测试集AUC
                test_df = pd.read_csv('simple_combined.csv')
                pred_df = pd.read_csv(pred_path)
                
                test_mask = test_df['split'] == 'test'
                test_data = test_df[test_mask]
                test_preds = pred_df[test_mask]
                
                label_cols = [col for col in test_data.columns if col not in ['smiles', 'split']]
                auc_scores = []
                
                for col in label_cols:
                    if col in test_preds.columns:
                        try:
                            y_true = test_data[col].values
                            y_pred = test_preds[col].values
                            
                            if len(np.unique(y_true)) > 1:
                                auc = roc_auc_score(y_true, y_pred)
                                auc_scores.append(auc)
                        except:
                            continue
                
                if auc_scores:
                    return np.mean(auc_scores)
                    
            return None
            
        except Exception as e:
            return None
    
    def compute_ensemble_auc(self, ensemble_models):
        """计算集成AUC"""
        
        try:
            # 读取所有预测
            all_preds = []
            
            for model_dir, _ in ensemble_models:
                pred_path = f"{model_dir}_predictions.csv"
                if os.path.exists(pred_path):
                    pred_df = pd.read_csv(pred_path)
                    test_df = pd.read_csv('simple_combined.csv')
                    
                    test_mask = test_df['split'] == 'test'
                    test_preds = pred_df[test_mask]
                    
                    label_cols = [col for col in test_preds.columns if col != 'smiles']
                    pred_values = test_preds[label_cols].values
                    all_preds.append(pred_values)
            
            if len(all_preds) >= 2:
                # 平均集成
                ensemble_pred = np.mean(all_preds, axis=0)
                
                # 计算AUC
                test_df = pd.read_csv('simple_combined.csv')
                test_mask = test_df['split'] == 'test'
                test_data = test_df[test_mask]
                
                label_cols = [col for col in test_data.columns if col not in ['smiles', 'split']]
                auc_scores = []
                
                for i, col in enumerate(label_cols):
                    try:
                        y_true = test_data[col].values
                        y_pred = ensemble_pred[:, i]
                        
                        if len(np.unique(y_true)) > 1:
                            auc = roc_auc_score(y_true, y_pred)
                            auc_scores.append(auc)
                    except:
                        continue
                
                if auc_scores:
                    return np.mean(auc_scores)
            
            return None
            
        except Exception as e:
            return None
    
    def run_optimization(self):
        """运行优化"""
        
        start_time = time.time()
        
        print("开始简化优化实验")
        print(f"基线AUC: {self.baseline_auc}")
        print("=" * 60)
        
        success_count = 0
        
        # 步骤1: 基础数据分析
        if self.basic_data_analysis():
            success_count += 1
        
        # 步骤2: 架构测试
        if self.architecture_quick_test():
            success_count += 1
        
        # 步骤3: 超参数搜索
        if self.hyperparameter_grid_search():
            success_count += 1
        
        # 步骤4: 集成学习
        if self.simple_ensemble():
            success_count += 1
        
        # 生成报告
        self.generate_report(start_time, success_count)
        
        return self.results
    
    def generate_report(self, start_time, success_count):
        """生成报告"""
        
        total_time = time.time() - start_time
        
        print(f"\n=== 优化实验完成 ===")
        print(f"成功完成: {success_count}/4 个步骤")
        print(f"总用时: {total_time:.1f}秒")
        
        # 分析最佳结果
        best_improvements = []
        
        if 'architecture' in self.results:
            arch_best = self.results['architecture']['best_config']
            best_improvements.append(('架构优化', arch_best['improvement']))
        
        if 'hyperparameters' in self.results:
            hyper_best = self.results['hyperparameters']['best_config']
            best_improvements.append(('超参数优化', hyper_best['improvement']))
        
        if 'ensemble' in self.results:
            ensemble_imp = self.results['ensemble']['improvement']
            best_improvements.append(('集成学习', ensemble_imp))
        
        if best_improvements:
            print(f"\n最佳改进:")
            for method, improvement in sorted(best_improvements, key=lambda x: x[1], reverse=True):
                print(f"  {method}: {improvement:+.2f}%")
        
        # 保存结果
        final_report = {
            'baseline_auc': self.baseline_auc,
            'optimization_date': datetime.now().isoformat(),
            'total_runtime': total_time,
            'success_count': success_count,
            'results': self.results
        }
        
        with open('simplified_optimization_report.json', 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细报告已保存: simplified_optimization_report.json")

def main():
    """主函数"""
    
    optimizer = SimplifiedOptimizer()
    results = optimizer.run_optimization()
    
    return results

if __name__ == "__main__":
    main()
