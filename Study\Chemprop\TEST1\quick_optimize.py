#!/usr/bin/env python3
"""
快速优化脚本 - 立即开始第一个优化实验
基于当前基线模型(ROC-AUC=0.7225)进行改进
"""

import subprocess
import os
import time
from datetime import datetime

def run_chemprop_command(cmd, description):
    """运行ChemProp命令并显示进度"""
    print(f"\n🚀 {description}")
    print("=" * 60)
    print(f"命令: {' '.join(cmd)}")
    print(f"开始时间: {datetime.now().strftime('%H:%M:%S')}")
    print()
    
    start_time = time.time()
    
    try:
        # 运行命令
        result = subprocess.run(cmd, cwd=os.getcwd(), text=True)
        
        end_time = time.time()
        duration = (end_time - start_time) / 60
        
        if result.returncode == 0:
            print(f"\n✅ {description} 完成!")
            print(f"⏱️ 耗时: {duration:.1f} 分钟")
            return True
        else:
            print(f"\n❌ {description} 失败!")
            return False
            
    except Exception as e:
        print(f"\n💥 执行异常: {e}")
        return False

def optimize_v1_deeper_wider():
    """优化实验1: 更深更宽的网络"""
    cmd = [
        "chemprop", "train",
        "--data-path", "TEST1/train.csv",
        "--task-type", "classification", 
        "--output-dir", "TEST1/optimized_v1_deeper_wider",
        "--epochs", "50",                    # 增加训练轮数
        "--batch-size", "16",                # 减小批次大小
        "--message-hidden-dim", "500",       # 增加隐藏层维度
        "--depth", "4",                      # 增加网络深度
        "--dropout", "0.15",                 # 增加dropout
        "--ffn-hidden-dim", "400",           # 增加FFN维度
        "--ffn-num-layers", "2",             # 增加FFN层数
        "--init-lr", "0.00005",              # 更小的初始学习率
        "--max-lr", "0.0005",                # 更小的最大学习率
        "--warmup-epochs", "5",              # 增加预热轮数
        "--split", "RANDOM",
        "--split-sizes", "0.8", "0.1", "0.1",
        "--data-seed", "42",
        "--num-workers", "0"
    ]
    
    return run_chemprop_command(cmd, "优化实验1: 更深更宽的网络")

def optimize_v2_longer_training():
    """优化实验2: 更长训练时间"""
    cmd = [
        "chemprop", "train",
        "--data-path", "TEST1/train.csv", 
        "--task-type", "classification",
        "--output-dir", "TEST1/optimized_v2_longer_training",
        "--epochs", "80",                    # 更长训练
        "--batch-size", "24",                # 中等批次大小
        "--message-hidden-dim", "400",       # 适中的隐藏层
        "--depth", "5",                      # 更深的网络
        "--dropout", "0.2",                  # 更强的正则化
        "--init-lr", "0.00003",              # 更小的学习率
        "--max-lr", "0.0003",
        "--final-lr", "0.00001",             # 更小的最终学习率
        "--warmup-epochs", "8",              # 更长预热
        "--split", "RANDOM",
        "--split-sizes", "0.8", "0.1", "0.1",
        "--data-seed", "42",
        "--num-workers", "0"
    ]
    
    return run_chemprop_command(cmd, "优化实验2: 更长训练时间")

def optimize_v3_balanced():
    """优化实验3: 平衡配置"""
    cmd = [
        "chemprop", "train",
        "--data-path", "TEST1/train.csv",
        "--task-type", "classification",
        "--output-dir", "TEST1/optimized_v3_balanced", 
        "--epochs", "60",                    # 平衡的训练轮数
        "--batch-size", "20",                # 平衡的批次大小
        "--message-hidden-dim", "450",       # 平衡的隐藏层
        "--depth", "4",                      # 适中深度
        "--dropout", "0.18",                 # 适中正则化
        "--ffn-hidden-dim", "350",           # 适中FFN
        "--ffn-num-layers", "2",
        "--init-lr", "0.00004",              # 平衡学习率
        "--max-lr", "0.0004",
        "--warmup-epochs", "6",
        "--split", "RANDOM",
        "--split-sizes", "0.8", "0.1", "0.1",
        "--data-seed", "42",
        "--num-workers", "0"
    ]
    
    return run_chemprop_command(cmd, "优化实验3: 平衡配置")

def check_results():
    """检查优化结果"""
    print("\n📊 检查优化结果")
    print("=" * 60)
    
    # 检查各个优化模型的结果文件
    models = [
        ("基线模型", "TEST1/fragrance_model"),
        ("优化v1", "TEST1/optimized_v1_deeper_wider"), 
        ("优化v2", "TEST1/optimized_v2_longer_training"),
        ("优化v3", "TEST1/optimized_v3_balanced")
    ]
    
    results = []
    
    for name, path in models:
        config_file = f"{path}/config.toml"
        if os.path.exists(config_file):
            print(f"✅ {name}: 模型已训练完成")
            # 这里可以添加解析结果的代码
            results.append((name, path))
        else:
            print(f"⏳ {name}: 训练中或未开始")
    
    return results

def main():
    """主函数 - 运行优化实验"""
    print("🎯 香气分子模型快速优化")
    print("=" * 60)
    print("目标: 将ROC-AUC从0.7225提升到>0.80")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 显示优化策略
    print("📋 优化策略:")
    print("1. 实验1: 更深更宽网络 (depth=4, hidden=500)")
    print("2. 实验2: 更长训练时间 (80轮, depth=5)")  
    print("3. 实验3: 平衡配置 (综合优化)")
    print()
    
    # 询问用户选择
    print("请选择要运行的实验:")
    print("1 - 运行实验1 (预计30-40分钟)")
    print("2 - 运行实验2 (预计60-80分钟)")
    print("3 - 运行实验3 (预计40-50分钟)")
    print("4 - 运行所有实验 (预计2-3小时)")
    print("5 - 检查现有结果")
    
    choice = input("\n请输入选择 (1-5): ").strip()
    
    if choice == "1":
        print("\n🚀 开始运行实验1...")
        optimize_v1_deeper_wider()
        
    elif choice == "2":
        print("\n🚀 开始运行实验2...")
        optimize_v2_longer_training()
        
    elif choice == "3":
        print("\n🚀 开始运行实验3...")
        optimize_v3_balanced()
        
    elif choice == "4":
        print("\n🚀 开始运行所有实验...")
        print("注意: 这将需要2-3小时完成")
        confirm = input("确认继续? (y/N): ").strip().lower()
        
        if confirm == 'y':
            optimize_v1_deeper_wider()
            optimize_v2_longer_training() 
            optimize_v3_balanced()
        else:
            print("已取消")
            
    elif choice == "5":
        check_results()
        
    else:
        print("无效选择")
        return
    
    print(f"\n🏁 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n💡 提示:")
    print("- 训练完成后可以查看各模型目录下的结果")
    print("- 使用 'python quick_optimize.py' 选择5来检查结果")
    print("- 最佳模型可用于后续预测任务")

if __name__ == "__main__":
    main()
