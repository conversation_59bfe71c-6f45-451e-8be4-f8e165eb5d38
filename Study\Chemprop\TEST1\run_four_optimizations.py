#!/usr/bin/env python3
"""
运行四个优化方向的简化执行脚本
基于特征增强实验结果，专注于其他三个优化方向
"""

import os
import json
import time
from datetime import datetime

def run_data_quality_analysis():
    """运行数据质量分析"""
    
    print("🔍 步骤1: 数据质量分析")
    print("=" * 60)
    
    try:
        import subprocess
        result = subprocess.run(['python', 'data_quality_enhancement.py'], 
                              capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ 数据质量分析完成")
            return True
        else:
            print(f"❌ 数据质量分析失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 数据质量分析异常: {e}")
        return False

def run_architecture_exploration():
    """运行架构探索"""
    
    print("\n🧠 步骤2: 网络架构探索")
    print("=" * 60)
    
    try:
        import subprocess
        result = subprocess.run(['python', 'architecture_exploration.py'], 
                              capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print("✅ 架构探索完成")
            return True
        else:
            print(f"❌ 架构探索失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 架构探索异常: {e}")
        return False

def run_hyperparameter_optimization():
    """运行超参数优化"""
    
    print("\n🔧 步骤3: 聚焦超参数优化")
    print("=" * 60)
    
    try:
        import subprocess
        result = subprocess.run(['python', 'focused_hyperparameter_optimization.py'], 
                              capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print("✅ 超参数优化完成")
            return True
        else:
            print(f"❌ 超参数优化失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 超参数优化异常: {e}")
        return False

def run_ensemble_learning():
    """运行集成学习"""
    
    print("\n🎯 步骤4: 智能集成学习")
    print("=" * 60)
    
    try:
        import subprocess
        result = subprocess.run(['python', 'smart_ensemble_learning.py'], 
                              capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print("✅ 集成学习完成")
            return True
        else:
            print(f"❌ 集成学习失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 集成学习异常: {e}")
        return False

def collect_and_analyze_results():
    """收集和分析所有结果"""
    
    print("\n📊 收集和分析结果")
    print("=" * 60)
    
    results = {
        'baseline_auc': 0.7539,
        'optimization_date': datetime.now().isoformat(),
        'results': {}
    }
    
    # 收集数据质量结果
    if os.path.exists('data_quality_report.json'):
        with open('data_quality_report.json', 'r', encoding='utf-8') as f:
            results['results']['data_quality'] = json.load(f)
        print("✅ 数据质量报告已收集")
    
    # 收集架构探索结果
    if os.path.exists('architecture_exploration_results.json'):
        with open('architecture_exploration_results.json', 'r', encoding='utf-8') as f:
            results['results']['architecture'] = json.load(f)
        print("✅ 架构探索结果已收集")
    
    # 收集超参数优化结果
    if os.path.exists('focused_hyperopt_results.json'):
        with open('focused_hyperopt_results.json', 'r', encoding='utf-8') as f:
            results['results']['hyperparameters'] = json.load(f)
        print("✅ 超参数优化结果已收集")
    
    # 收集集成学习结果
    if os.path.exists('smart_ensemble_results.json'):
        with open('smart_ensemble_results.json', 'r', encoding='utf-8') as f:
            results['results']['ensemble'] = json.load(f)
        print("✅ 集成学习结果已收集")
    
    return results

def generate_final_recommendations(results):
    """生成最终建议"""
    
    print("\n💡 生成最终优化建议")
    print("=" * 60)
    
    baseline_auc = results['baseline_auc']
    recommendations = []
    
    # 分析各个方向的效果
    arch_results = results['results'].get('architecture', {})
    hyper_results = results['results'].get('hyperparameters', {})
    ensemble_results = results['results'].get('ensemble', {})
    
    # 架构优化效果
    arch_improvement = 0
    if arch_results.get('best_architecture'):
        best_arch_auc = arch_results['best_architecture'].get('auc', baseline_auc)
        arch_improvement = (best_arch_auc - baseline_auc) / baseline_auc * 100
    
    # 超参数优化效果
    hyper_improvement = 0
    if hyper_results.get('best_auc'):
        hyper_improvement = (hyper_results['best_auc'] - baseline_auc) / baseline_auc * 100
    
    # 集成学习效果
    ensemble_improvement = 0
    if ensemble_results.get('ensemble_auc'):
        ensemble_improvement = (ensemble_results['ensemble_auc'] - baseline_auc) / baseline_auc * 100
    
    print(f"📈 各优化方向效果:")
    print(f"   架构优化: {arch_improvement:+.2f}%")
    print(f"   超参数优化: {hyper_improvement:+.2f}%")
    print(f"   集成学习: {ensemble_improvement:+.2f}%")
    
    # 生成建议
    if ensemble_improvement > max(arch_improvement, hyper_improvement) and ensemble_improvement > 1.0:
        recommendations.append({
            'priority': 'HIGH',
            'method': '集成学习',
            'improvement': f"{ensemble_improvement:+.2f}%",
            'description': '集成学习显示最佳效果，建议采用多模型集成策略'
        })
    
    if hyper_improvement > 1.0:
        recommendations.append({
            'priority': 'HIGH',
            'method': '超参数优化',
            'improvement': f"{hyper_improvement:+.2f}%",
            'description': '超参数优化有效，建议采用优化后的配置'
        })
    
    if arch_improvement > 1.0:
        recommendations.append({
            'priority': 'MEDIUM',
            'method': '架构优化',
            'improvement': f"{arch_improvement:+.2f}%",
            'description': '网络架构调整有效，建议采用最佳架构配置'
        })
    
    # 数据质量建议
    data_quality = results['results'].get('data_quality', {})
    if data_quality:
        recommendations.append({
            'priority': 'MEDIUM',
            'method': '数据质量提升',
            'improvement': '预期+2-5%',
            'description': '数据质量分析完成，建议根据报告进行数据清理'
        })
    
    # 如果没有显著改进
    if not any(imp > 1.0 for imp in [arch_improvement, hyper_improvement, ensemble_improvement]):
        recommendations.append({
            'priority': 'INFO',
            'method': '当前配置已优化',
            'improvement': '0%',
            'description': '您的基线配置(0.7539)已经相当优秀，建议专注于数据质量和特征工程'
        })
    
    # 打印建议
    print(f"\n🎯 最终优化建议:")
    for i, rec in enumerate(recommendations, 1):
        priority_emoji = {'HIGH': '🔥', 'MEDIUM': '⚡', 'INFO': 'ℹ️'}.get(rec['priority'], '📝')
        print(f"   {i}. {priority_emoji} {rec['method']} ({rec['improvement']})")
        print(f"      {rec['description']}")
    
    return recommendations

def main():
    """主函数"""
    
    start_time = time.time()
    
    print("🚀 开始四个方向的优化实验")
    print("🎯 目标: 超越基线 AUC 0.7539")
    print("📝 基于特征增强实验结果，专注于其他优化方向")
    print("=" * 80)
    
    # 检查必要文件
    required_files = ['train.csv', 'val.csv', 'test.csv']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return
    
    success_count = 0
    
    # 步骤1: 数据质量分析
    if run_data_quality_analysis():
        success_count += 1
    
    # 步骤2: 架构探索
    if run_architecture_exploration():
        success_count += 1
    
    # 步骤3: 超参数优化
    if run_hyperparameter_optimization():
        success_count += 1
    
    # 步骤4: 集成学习
    if run_ensemble_learning():
        success_count += 1
    
    # 收集和分析结果
    results = collect_and_analyze_results()
    recommendations = generate_final_recommendations(results)
    
    # 保存综合结果
    final_results = {
        'summary': {
            'baseline_auc': 0.7539,
            'total_optimizations': 4,
            'successful_optimizations': success_count,
            'optimization_date': datetime.now().isoformat(),
            'total_runtime': time.time() - start_time
        },
        'detailed_results': results,
        'recommendations': recommendations,
        'conclusion': {
            'feature_enhancement': '实验证明不需要额外分子特征，D-MPNN已足够强大',
            'next_steps': [
                '根据优先级实施建议的优化方法',
                '如果集成学习效果最佳，部署集成模型',
                '持续监控模型性能并进行微调',
                '考虑收集更多高质量训练数据'
            ]
        }
    }
    
    with open('four_optimizations_final_report.json', 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    total_time = time.time() - start_time
    
    print(f"\n🎉 四个方向优化实验完成!")
    print(f"⏱️ 总用时: {total_time:.1f}秒")
    print(f"✅ 成功完成: {success_count}/4 个优化方向")
    print(f"📊 详细报告: four_optimizations_final_report.json")
    
    print(f"\n📋 关键结论:")
    print(f"   ❌ 分子特征增强: 不需要，反而有负面影响")
    print(f"   ✅ 其他三个方向: 根据实验结果选择最佳策略")
    print(f"   🎯 您的直觉是正确的: D-MPNN本身就足够强大!")

if __name__ == "__main__":
    main()
