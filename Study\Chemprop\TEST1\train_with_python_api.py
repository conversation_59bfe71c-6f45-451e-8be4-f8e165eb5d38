#!/usr/bin/env python3
"""
使用ChemProp Python API训练香气分子模型
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加ChemProp路径
sys.path.append(str(Path(__file__).parent.parent / "chemprop_env" / "Lib" / "site-packages"))

try:
    import chemprop
    print(f"✅ ChemProp 已导入，版本: {chemprop.__version__}")
except ImportError as e:
    print(f"❌ 无法导入 ChemProp: {e}")
    sys.exit(1)

def prepare_data_for_training():
    """准备训练数据"""
    print("=== 准备训练数据 ===")
    
    # 检查数据文件
    files = ['train.csv', 'val.csv', 'test.csv']
    for file in files:
        if not Path(file).exists():
            print(f"❌ 缺少文件: {file}")
            return False
    
    # 读取数据
    train_df = pd.read_csv('train.csv')
    val_df = pd.read_csv('val.csv')
    test_df = pd.read_csv('test.csv')
    
    print(f"✅ 数据加载成功:")
    print(f"  训练集: {len(train_df)} 个分子")
    print(f"  验证集: {len(val_df)} 个分子")
    print(f"  测试集: {len(test_df)} 个分子")
    print(f"  特征数: {len(train_df.columns) - 1}")  # 减去SMILES列
    
    return True

def train_fragrance_model():
    """训练香气分子模型"""
    print("\n=== 训练香气分子模型 ===")
    
    try:
        from chemprop import train
        
        # 配置训练参数
        args = [
            '--data-path', 'train.csv',
            '--separate-val-path', 'val.csv',
            '--separate-test-path', 'test.csv',
            '--task-type', 'classification',
            '--save-dir', 'fragrance_model_api',
            '--epochs', '30',
            '--batch-size', '32',
            '--hidden-size', '300',
            '--depth', '3',
            '--dropout', '0.1',
            '--seed', '42',
            '--quiet'
        ]
        
        print("🚀 开始训练...")
        print(f"参数: {' '.join(args)}")
        
        # 执行训练
        train.main(args)
        
        print("✅ 训练完成!")
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False

def predict_with_model():
    """使用训练好的模型进行预测"""
    print("\n=== 模型预测 ===")
    
    try:
        from chemprop import predict
        
        # 检查模型是否存在
        model_dir = Path('fragrance_model_api')
        if not model_dir.exists():
            print("❌ 找不到训练好的模型")
            return False
        
        # 配置预测参数
        args = [
            '--test-path', 'test.csv',
            '--checkpoint-dir', 'fragrance_model_api',
            '--preds-path', 'fragrance_predictions.csv'
        ]
        
        print("🔮 开始预测...")
        print(f"参数: {' '.join(args)}")
        
        # 执行预测
        predict.main(args)
        
        print("✅ 预测完成!")
        return True
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        return False

def analyze_results():
    """分析结果"""
    print("\n=== 结果分析 ===")
    
    # 检查预测文件
    pred_file = Path('fragrance_predictions.csv')
    if pred_file.exists():
        pred_df = pd.read_csv(pred_file)
        print(f"✅ 预测结果: {len(pred_df)} 个分子")
        
        # 简单统计
        numeric_cols = pred_df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"📊 预测特征数: {len(numeric_cols)}")
            
            # 显示一些统计信息
            for col in numeric_cols[:5]:  # 显示前5个特征
                mean_val = pred_df[col].mean()
                print(f"  {col}: 平均值 = {mean_val:.3f}")
    
    # 检查模型文件
    model_dir = Path('fragrance_model_api')
    if model_dir.exists():
        print(f"✅ 模型保存在: {model_dir}")
        
        # 列出模型文件
        model_files = list(model_dir.rglob("*.pt"))
        if model_files:
            print(f"📋 模型文件: {len(model_files)} 个")
    
    print("\n🎯 训练总结:")
    print("  - 任务类型: 多标签分类")
    print("  - 香气特征: 138个")
    print("  - 模型架构: 图神经网络")
    print("  - 训练方法: ChemProp Python API")

def main():
    """主函数"""
    print("🧪 香气分子多标签分类 - Python API")
    print("=" * 60)
    
    # 检查数据
    if not prepare_data_for_training():
        print("❌ 数据准备失败")
        return
    
    # 训练模型
    if train_fragrance_model():
        print("🎉 训练成功!")
        
        # 进行预测
        if predict_with_model():
            print("🎉 预测成功!")
            
            # 分析结果
            analyze_results()
            
            print("\n🎉 完整流程执行成功!")
        else:
            print("⚠️  训练成功但预测失败")
    else:
        print("❌ 训练失败")

if __name__ == "__main__":
    main()
