#!/usr/bin/env python3
"""
最小化特征增强 - 只添加香气感知最关键的特征
专注于香气分子分类的核心物理化学性质
"""

import pandas as pd
import numpy as np
from rdkit import Chem
from rdkit.Chem import Descriptors, Crippen
from rdkit.Chem.rdMolDescriptors import CalcTPSA, CalcNumRotatableBonds
import subprocess
import os

class MinimalFragranceFeatures:
    """最小化香气特征提取器 - 只关注香气感知的核心特征"""
    
    def extract_core_fragrance_features(self, smiles_list):
        """提取香气感知的核心特征（仅5个关键特征）"""
        features = []
        
        for smiles in smiles_list:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                features.append([0, 0, 0, 0, 0])
                continue
            
            # 1. 挥发性指数 (Volatility Index)
            # 香气分子必须能挥发到达嗅觉受体
            mw = Descriptors.MolWt(mol)
            volatility = 1000 / (mw + 50)  # 分子量越小越易挥发
            
            # 2. 疏水性 (Hydrophobicity) 
            # 影响分子在鼻腔脂质中的分配
            logp = Crippen.MolLogP(mol)
            
            # 3. 极性表面积比例 (Polar Surface Area Ratio)
            # 影响分子与受体的相互作用
            tpsa = CalcTPSA(mol)
            psa_ratio = tpsa / max(mw, 1) * 100
            
            # 4. 分子柔性 (Molecular Flexibility)
            # 影响分子与受体的构象适配
            rotatable_bonds = CalcNumRotatableBonds(mol)
            flexibility = rotatable_bonds / max(mol.GetNumBonds(), 1)
            
            # 5. 芳香性指数 (Aromaticity Index)
            # 芳香环对香气强度有重要影响
            aromatic_atoms = sum(1 for atom in mol.GetAtoms() if atom.GetIsAromatic())
            aromaticity = aromatic_atoms / max(mol.GetNumAtoms(), 1)
            
            features.append([volatility, logp, psa_ratio, flexibility, aromaticity])
        
        feature_names = [
            'volatility_index',    # 挥发性指数
            'hydrophobicity',      # 疏水性
            'polar_surface_ratio', # 极性表面积比例
            'molecular_flexibility', # 分子柔性
            'aromaticity_index'    # 芳香性指数
        ]
        
        return pd.DataFrame(features, columns=feature_names)
    
    def create_minimal_enhanced_dataset(self, input_csv, output_csv):
        """创建最小化增强数据集"""
        print(f"📊 处理数据集: {input_csv}")
        df = pd.read_csv(input_csv)
        
        print("🧬 提取核心香气特征...")
        fragrance_features = self.extract_core_fragrance_features(df['smiles'])
        
        # 合并特征
        enhanced_df = pd.concat([df, fragrance_features], axis=1)
        
        print(f"✅ 特征增强完成！")
        print(f"   原始特征: {len(df.columns)} → 增强后: {len(enhanced_df.columns)} (+{len(fragrance_features.columns)})")
        
        # 保存增强数据集
        enhanced_df.to_csv(output_csv, index=False)
        print(f"💾 保存至: {output_csv}")
        
        return enhanced_df

def compare_with_without_features():
    """对比有无特征增强的性能差异"""
    
    print("🔬 开始对比实验：有无特征增强的性能差异")
    print("=" * 60)
    
    # 创建特征提取器
    feature_extractor = MinimalFragranceFeatures()
    
    # 1. 创建增强特征数据集
    print("\n📈 步骤1: 创建增强特征数据集")
    datasets = ['train.csv', 'val.csv', 'test.csv']
    
    for dataset in datasets:
        input_path = f"TEST1/{dataset}"
        output_path = f"TEST1/minimal_enhanced_{dataset}"
        
        if os.path.exists(input_path):
            feature_extractor.create_minimal_enhanced_dataset(input_path, output_path)
    
    # 2. 训练原始模型（无特征增强）
    print("\n🤖 步骤2: 训练原始模型（无特征增强）")
    
    original_cmd = [
        'chemprop', 'train',
        '--data-path', 'TEST1/train.csv',
        '--separate-val-path', 'TEST1/val.csv',
        '--separate-test-path', 'TEST1/test.csv',
        '--task-type', 'classification',
        '--output-dir', 'TEST1/comparison_original',
        '--epochs', '80',
        '--batch-size', '24',
        '--message-hidden-dim', '400',
        '--depth', '5',
        '--dropout', '0.2',
        '--init-lr', '0.00001',
        '--max-lr', '0.0001',
        '--final-lr', '0.000005',
        '--warmup-epochs', '5',
        '--seed', '42',
        '--save-smiles-splits'
    ]
    
    try:
        print("🚀 训练原始模型...")
        result = subprocess.run(original_cmd, capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print("✅ 原始模型训练完成")
            
            # 读取原始模型结果
            original_scores_path = "TEST1/comparison_original/test_scores.csv"
            if os.path.exists(original_scores_path):
                original_scores = pd.read_csv(original_scores_path)
                original_auc = original_scores['auc'].mean()
                print(f"📊 原始模型 ROC-AUC: {original_auc:.4f}")
            else:
                original_auc = None
                print("⚠️ 无法读取原始模型测试结果")
        else:
            print(f"❌ 原始模型训练失败: {result.stderr}")
            original_auc = None
            
    except subprocess.TimeoutExpired:
        print("⏰ 原始模型训练超时")
        original_auc = None
    except Exception as e:
        print(f"❌ 原始模型训练异常: {e}")
        original_auc = None
    
    # 3. 训练增强特征模型
    print("\n🧬 步骤3: 训练增强特征模型")
    
    enhanced_cmd = [
        'chemprop', 'train',
        '--data-path', 'TEST1/minimal_enhanced_train.csv',
        '--separate-val-path', 'TEST1/minimal_enhanced_val.csv',
        '--separate-test-path', 'TEST1/minimal_enhanced_test.csv',
        '--task-type', 'classification',
        '--output-dir', 'TEST1/comparison_enhanced',
        '--epochs', '80',
        '--batch-size', '24',
        '--message-hidden-dim', '400',
        '--depth', '5',
        '--dropout', '0.2',
        '--init-lr', '0.00001',
        '--max-lr', '0.0001',
        '--final-lr', '0.000005',
        '--warmup-epochs', '5',
        '--seed', '42',
        '--save-smiles-splits'
    ]
    
    try:
        print("🚀 训练增强特征模型...")
        result = subprocess.run(enhanced_cmd, capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print("✅ 增强特征模型训练完成")
            
            # 读取增强模型结果
            enhanced_scores_path = "TEST1/comparison_enhanced/test_scores.csv"
            if os.path.exists(enhanced_scores_path):
                enhanced_scores = pd.read_csv(enhanced_scores_path)
                enhanced_auc = enhanced_scores['auc'].mean()
                print(f"📊 增强特征模型 ROC-AUC: {enhanced_auc:.4f}")
            else:
                enhanced_auc = None
                print("⚠️ 无法读取增强模型测试结果")
        else:
            print(f"❌ 增强特征模型训练失败: {result.stderr}")
            enhanced_auc = None
            
    except subprocess.TimeoutExpired:
        print("⏰ 增强特征模型训练超时")
        enhanced_auc = None
    except Exception as e:
        print(f"❌ 增强特征模型训练异常: {e}")
        enhanced_auc = None
    
    # 4. 对比结果
    print("\n📊 步骤4: 对比分析结果")
    print("=" * 60)
    
    if original_auc is not None and enhanced_auc is not None:
        improvement = (enhanced_auc - original_auc) / original_auc * 100
        
        print(f"🔍 详细对比结果:")
        print(f"   原始模型 ROC-AUC:     {original_auc:.4f}")
        print(f"   增强特征模型 ROC-AUC: {enhanced_auc:.4f}")
        print(f"   性能提升:             {improvement:+.2f}%")
        
        if improvement > 0:
            print(f"\n✅ 结论: 特征增强有效！提升了 {improvement:.2f}%")
            print("💡 这证明了香气相关的物理化学特征对分类任务有帮助")
        else:
            print(f"\n❌ 结论: 特征增强无效果或有负面影响 ({improvement:.2f}%)")
            print("💡 可能原因: 1) 特征不够相关 2) 模型已经足够强大 3) 过拟合")
        
        # 保存对比结果
        comparison_results = {
            'original_auc': original_auc,
            'enhanced_auc': enhanced_auc,
            'improvement_percent': improvement,
            'features_added': [
                'volatility_index',
                'hydrophobicity', 
                'polar_surface_ratio',
                'molecular_flexibility',
                'aromaticity_index'
            ]
        }
        
        import json
        with open('TEST1/feature_enhancement_comparison.json', 'w') as f:
            json.dump(comparison_results, f, indent=2)
        
        print(f"\n💾 对比结果已保存: TEST1/feature_enhancement_comparison.json")
        
    else:
        print("❌ 无法完成对比，因为某个模型训练失败")
    
    print("\n" + "=" * 60)
    print("🎯 实验总结:")
    print("   这个实验帮助我们理解特征增强是否真的有必要")
    print("   如果提升明显，说明D-MPNN确实遗漏了重要的香气相关信息")
    print("   如果提升微小，说明D-MPNN已经能够很好地捕获香气特征")

if __name__ == "__main__":
    compare_with_without_features()
