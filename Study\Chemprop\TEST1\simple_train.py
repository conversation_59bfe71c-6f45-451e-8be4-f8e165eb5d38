#!/usr/bin/env python3
"""
简化的香气分子训练脚本
使用ChemProp 2.2.0进行多标签分类
"""

import subprocess
import sys
import time
from pathlib import Path

def run_training():
    """运行ChemProp训练"""
    print("=== 香气分子多标签分类训练 ===")
    
    # 检查数据文件
    if not Path("train.csv").exists():
        print("❌ 找不到 train.csv")
        return False
    
    print("✅ 数据文件检查通过")
    
    # 创建输出目录
    output_dir = "fragrance_model"
    Path(output_dir).mkdir(exist_ok=True)
    
    # 构建训练命令 - 使用正确的ChemProp 2.2.0参数
    cmd = [
        "chemprop", "train",
        "--data-path", "train.csv",
        "--task-type", "classification",  # 多标签分类
        "--output-dir", output_dir,
        "--epochs", "30",  # 减少训练轮数以加快速度
        "--batch-size", "32",
        "--message-hidden-dim", "300",
        "--depth", "3",
        "--dropout", "0.1",
        "--split", "RANDOM",  # 使用split而不是split-type
        "--split-sizes", "0.8", "0.1", "0.1",
        "--data-seed", "42",
        "--num-workers", "0"  # 避免多进程问题
    ]
    
    print("🚀 开始训练...")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # 运行训练命令
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True,
            timeout=1800  # 30分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 训练成功完成!")
        print(f"⏱️  训练时间: {duration:.1f}秒")
        
        if result.stdout:
            print("\n📊 训练输出:")
            print(result.stdout[-1000:])  # 显示最后1000个字符
        
        # 检查生成的模型文件
        model_path = Path(output_dir)
        if (model_path / "model_0").exists():
            print(f"\n✅ 模型已保存到: {output_dir}")
            
            # 列出生成的文件
            print("\n📋 生成的文件:")
            for file in model_path.rglob("*"):
                if file.is_file():
                    print(f"  - {file.relative_to(model_path)}")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 训练超时 (30分钟)")
        return False
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"❌ 训练失败!")
        print(f"⏱️  运行时间: {duration:.1f}秒")
        print(f"错误代码: {e.returncode}")
        
        if e.stdout:
            print(f"\n标准输出:")
            print(e.stdout)
        if e.stderr:
            print(f"\n错误输出:")
            print(e.stderr)
        
        return False

def predict_test_set():
    """预测测试集"""
    print("\n=== 预测测试集 ===")
    
    # 检查模型是否存在
    model_dir = Path("fragrance_model")
    if not model_dir.exists():
        print("❌ 找不到训练好的模型")
        return False
    
    # 检查测试数据
    if not Path("test.csv").exists():
        print("❌ 找不到测试数据")
        return False
    
    # 构建预测命令
    cmd = [
        "chemprop", "predict",
        "--test-path", "test.csv",
        "--checkpoint-dir", "fragrance_model",
        "--preds-path", "test_predictions.csv"
    ]
    
    print("🔮 开始预测...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True,
            timeout=300  # 5分钟超时
        )
        
        print("✅ 预测完成!")
        print("📁 预测结果保存在: test_predictions.csv")
        
        if result.stdout:
            print("\n📊 预测输出:")
            print(result.stdout)
        
        return True
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 香气分子多标签分类模型")
    print("=" * 50)
    
    # 检查ChemProp环境
    try:
        result = subprocess.run(
            ["chemprop", "--help"],
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ ChemProp 环境正常")
    except Exception as e:
        print(f"❌ ChemProp 环境问题: {e}")
        return
    
    # 执行训练
    if run_training():
        print("\n🎉 训练成功!")
        
        # 执行预测
        if predict_test_set():
            print("\n🎉 完整流程执行成功!")
            
            # 简单分析
            print("\n📊 结果摘要:")
            print("  - 训练数据: 3513个分子")
            print("  - 验证数据: 439个分子") 
            print("  - 测试数据: 440个分子")
            print("  - 香气特征: 138个")
            print("  - 任务类型: 多标签分类")
            print("  - 模型: 图神经网络 (ChemProp)")
        else:
            print("\n⚠️  训练成功但预测失败")
    else:
        print("\n❌ 训练失败")

if __name__ == "__main__":
    main()
